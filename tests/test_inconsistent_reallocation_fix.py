"""
Test suite to verify the InconsistentReallocation error fixes.

This module tests the specific fixes implemented to resolve the 
InconsistentReallocation error (0x9e36b890) by ensuring that:
1. Total withdrawals exactly equal total supplies
2. Remainder amounts are properly handled
3. Precision loss is minimized
4. Idle market allocation works correctly
"""

import unittest
from unittest.mock import Mock
from decimal import Decimal
from typing import Dict, Any

from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
from utils.reallocation_validator import (
    validate_reallocation_consistency,
)
from config import config


class MockMarketData:
    def __init__(self, market_id: str, vault_assets: int, state: Dict[str, int], 
                 params: Dict[str, Any], cap: int = 10**18):
        self.data = {
            "id": market_id,
            "vault_assets": vault_assets,
            "state": state,
            "cap": cap,
        }
        self.params = params
        self.state = state


class MockVaultData:
    def __init__(self, market_data_list):
        self.data = market_data_list
        
    def __getitem__(self, key):
        if key == "market_data":
            return self.data
        raise KeyError(key)


class TestInconsistentReallocationFix(unittest.TestCase):
    
    def setUp(self):
        self.strategy = PrioritizedUtilizationStrategy()
        
    def create_test_markets(self) -> list:
        """Create a set of test markets with various utilization scenarios."""
        markets = []
        
        # Market 1: Under-utilized (should withdraw from)
        markets.append(MockMarketData(
            market_id="0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81",  # PT-USR Sept
            vault_assets=1000000 * 10**6,  # 1M USDC
            state={
                "total_supply_assets": 10000000 * 10**6,  # 10M USDC
                "total_borrow_assets": 8000000 * 10**6,   # 8M USDC (80% utilization)
            },
            params={"loan_token": "0x123", "collateral_token": "0x456", "oracle": "0x789", "irm": "0xabc", "lltv": 900000000000000000},
            cap=2000000 * 10**6
        ))
        
        # Market 2: Over-utilized (should deposit to)
        markets.append(MockMarketData(
            market_id="0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c",  # PT-cUSDO Jul
            vault_assets=500000 * 10**6,   # 500K USDC
            state={
                "total_supply_assets": 5000000 * 10**6,   # 5M USDC
                "total_borrow_assets": 4800000 * 10**6,   # 4.8M USDC (96% utilization)
            },
            params={"loan_token": "0x123", "collateral_token": "0x457", "oracle": "0x789", "irm": "0xabc", "lltv": 900000000000000000},
            cap=1000000 * 10**6
        ))
        
        # Market 3: Idle market
        markets.append(MockMarketData(
            market_id="0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb",  # Idle
            vault_assets=100000 * 10**6,   # 100K USDC
            state={
                "total_supply_assets": 1000000 * 10**6,   # 1M USDC
                "total_borrow_assets": 1000000 * 10**6,   # 1M USDC (100% utilization)
            },
            params={"loan_token": "0x123", "collateral_token": "0x000", "oracle": "0x789", "irm": "0xabc", "lltv": 1000000000000000000},
            cap=10000000 * 10**6
        ))
        
        return markets
    
    def test_reallocation_consistency(self):
        """Test that reallocation maintains exact consistency between withdrawals and deposits."""
        markets = self.create_test_markets()
        vault_data = MockVaultData(markets)
        
        result = self.strategy.find_reallocation(vault_data)
        
        if result is None:
            self.skipTest("Strategy returned None - no reallocation needed")
        
        # Calculate total changes
        total_withdrawn = 0
        total_deposited = 0
        
        for i, (params, new_amount) in enumerate(result):
            if i < len(markets):
                original_amount = markets[i].data["vault_assets"]
                change = new_amount - original_amount
                
                if new_amount == config.MAX_UINT_256:
                    # Skip Idle market max allocation for this calculation
                    continue
                    
                if change < 0:
                    total_withdrawn += abs(change)
                elif change > 0:
                    total_deposited += change
        
        # The key test: withdrawals should equal deposits (within small tolerance for rounding)
        self.assertAlmostEqual(
            total_withdrawn, total_deposited, delta=1000,  # Allow 1000 wei tolerance
            msg=f"Withdrawals ({total_withdrawn}) should equal deposits ({total_deposited})"
        )
    
    def test_idle_market_handling(self):
        """Test that Idle market is properly handled with MAX_UINT_256."""
        markets = self.create_test_markets()
        vault_data = MockVaultData(markets)
        
        result = self.strategy.find_reallocation(vault_data)
        
        if result is None:
            self.skipTest("Strategy returned None - no reallocation needed")
        
        # Check if Idle market gets MAX_UINT_256 allocation
        idle_market_id = "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb"
        
        for params, new_amount in result:
            # This is a simplified check - in practice would need proper market ID matching
            if new_amount == config.MAX_UINT_256:
                self.assertEqual(new_amount, config.MAX_UINT_256, 
                               "Idle market should receive MAX_UINT_256 allocation")
                break
    
    def test_validation_utility(self):
        """Test the reallocation validation utility."""
        # Create a simple test case
        current_allocations = {
            "market1": 1000000,  # 1M
            "market2": 500000,   # 500K
        }
        
        # Valid reallocation: move 100K from market1 to market2
        valid_reallocation = [
            ({"market": "market1"}, 900000),   # -100K
            ({"market": "market2"}, 600000),   # +100K
        ]
        
        is_valid, error_msg = validate_reallocation_consistency(
            valid_reallocation, current_allocations
        )
        
        self.assertTrue(is_valid, f"Valid reallocation should pass: {error_msg}")
        self.assertEqual(changes["market1"], -100000)
        self.assertEqual(changes["market2"], 100000)
        
        # Invalid reallocation: inconsistent amounts
        invalid_reallocation = [
            ({"market": "market1"}, 900000),   # -100K
            ({"market": "market2"}, 650000),   # +150K (inconsistent!)
        ]
        
        is_valid, error_msg = validate_reallocation_consistency(
            invalid_reallocation, current_allocations
        )
        
        self.assertFalse(is_valid, "Invalid reallocation should fail")
        self.assertIn("Inconsistent", error_msg)
    
    def test_precision_loss_calculation(self):
        """Test precision loss risk calculation."""
        withdrawals = [1000000, 500000, 250000]  # Various withdrawal amounts
        deposits = [1750000]  # Single large deposit
        
        max_loss, loss_pct = calculate_precision_loss_risk(withdrawals, deposits)
        
        self.assertGreaterEqual(max_loss, 0, "Max loss should be non-negative")
        self.assertGreaterEqual(loss_pct, 0.0, "Loss percentage should be non-negative")
        self.assertLess(loss_pct, 1.0, "Loss percentage should be less than 1% for reasonable amounts")
    
    def test_edge_case_zero_amounts(self):
        """Test handling of edge cases with zero amounts."""
        markets = []
        
        # Market with zero vault assets
        markets.append(MockMarketData(
            market_id="0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81",
            vault_assets=0,  # Zero assets
            state={
                "total_supply_assets": 1000000 * 10**6,
                "total_borrow_assets": 900000 * 10**6,
            },
            params={"loan_token": "0x123", "collateral_token": "0x456", "oracle": "0x789", "irm": "0xabc", "lltv": 900000000000000000},
        ))
        
        vault_data = MockVaultData(markets)
        
        # Should not crash with zero amounts
        result = self.strategy.find_reallocation(vault_data)
        
        # Result can be None or a valid list, but should not crash
        if result is not None:
            self.assertIsInstance(result, list)
    
    def test_mathematical_precision(self):
        """Test that mathematical calculations maintain precision."""
        from utils.math import safe_mul_div, w_div_down, w_mul_down
        
        # Test safe_mul_div with various inputs
        result1 = safe_mul_div(1000000, 900000000000000000, 1000000000000000000)
        expected1 = 900000  # 1M * 0.9
        self.assertEqual(result1, expected1, "Safe multiplication should be precise")
        
        # Test division by zero safety
        result2 = safe_mul_div(1000000, 500000, 0)
        self.assertEqual(result2, 0, "Division by zero should return 0")
        
        # Test overflow protection
        large_x = 2**200
        large_y = 2**200
        result3 = safe_mul_div(large_x, large_y, 1000000000000000000)
        self.assertEqual(result3, 0, "Overflow should return 0")


if __name__ == "__main__":
    unittest.main(verbosity=2)
