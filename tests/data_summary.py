#!/usr/bin/env python3
"""
Data Summary Script

Provides a concise summary of what data is being fetched and how it's used.
"""

import sys
import os
from decimal import Decimal

# Add root directory and src to path
root_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, root_dir)
sys.path.insert(0, os.path.join(root_dir, 'src'))

from log import get_logger

logger = get_logger(__name__)

def main():
    """Main summary function."""
    from vault_data import VaultData
    from config import config
    from utils.math import get_utilization
    
    logger.info("📊 MORPHO REALLOCATOR BOT - DATA SUMMARY")
    logger.info("=" * 60)
    
    # Basic info
    logger.info(f"🏦 Vault: {config.VAULT_ADDRESS}")
    logger.info(f"🌐 Network: Base (Chain ID: {config.PRIMARY_CHAIN_ID})")
    logger.info(f"🔗 Morpho: {config.MORPHO_ADDRESS}")
    
    # Load vault data
    logger.info("\n🔍 Loading vault data...")
    vault_data = VaultData(config.VAULT_ADDRESS)
    markets = vault_data.data['market_data']
    
    logger.info(f"✅ Found {len(markets)} markets in vault")
    
    # Data being fetched for each market
    logger.info("\n📋 Data Fetched Per Market:")
    logger.info("  🔧 Market Parameters (from Morpho.idToMarketParams):")
    logger.info("     - Loan Token Address")
    logger.info("     - Collateral Token Address") 
    logger.info("     - Oracle Address")
    logger.info("     - Interest Rate Model (IRM) Address")
    logger.info("     - Loan-to-Value (LLTV) Ratio")
    
    logger.info("  📈 Market State (from Morpho.market):")
    logger.info("     - Total Supply Assets & Shares")
    logger.info("     - Total Borrow Assets & Shares")
    logger.info("     - Last Update Timestamp")
    logger.info("     - Fee Rate")
    
    logger.info("  🏦 Vault Position (from Morpho.position):")
    logger.info("     - Vault's Supply Shares in Market")
    logger.info("     - Calculated Vault Assets")
    
    logger.info("  ⚙️ Vault Config (from Vault.config):")
    logger.info("     - Supply Cap for Market")
    logger.info("     - Market Enabled Status")
    
    # Show current market status
    logger.info(f"\n📊 Current Market Status:")
    logger.info(f"{'Market ID':<12} {'Utilization':<12} {'Target':<8} {'Vault Assets':<15} {'Status'}")
    logger.info("-" * 70)
    
    total_vault_assets = 0
    markets_with_assets = 0
    
    for market in markets:
        market_id = market.data['id'][:10] + "..."
        utilization = get_utilization(market.state)
        target = config.target_util.get(market.data['id'], Decimal('0'))
        vault_assets = market.data['vault_assets']
        
        util_pct = f"{float(utilization)/1e18:.1%}"
        target_pct = f"{float(target)/1e18:.1%}" if target > 0 else "N/A"
        assets_str = f"{vault_assets/1e18:.2f} USDC"
        
        status = "🟢 Active" if vault_assets > 0 else "⚪ Empty"
        
        logger.info(f"{market_id:<12} {util_pct:<12} {target_pct:<8} {assets_str:<15} {status}")
        
        total_vault_assets += vault_assets
        if vault_assets > 0:
            markets_with_assets += 1
    
    logger.info("-" * 70)
    logger.info(f"Total Vault Assets: {total_vault_assets/1e18:.2f} USDC")
    logger.info(f"Active Markets: {markets_with_assets}/{len(markets)}")
    
    # Strategy configuration
    logger.info(f"\n🎯 Strategy Configuration:")
    logger.info(f"  Strategy: PrioritizedUtilizationStrategy")
    logger.info(f"  Markets with Targets: {len([m for m in config.target_util.keys() if config.target_util[m] > 0])}")
    logger.info(f"  Priority List Length: {len(config.priority_list)}")
    
    # Key Web3 calls made
    logger.info(f"\n🌐 Key Web3 Contract Calls Made:")
    logger.info(f"  Vault Contract ({config.VAULT_ADDRESS[:10]}...):")
    logger.info(f"    - withdrawQueueLength() → Get number of markets")
    logger.info(f"    - withdrawQueue(i) → Get market ID at index i")
    logger.info(f"    - config(marketId) → Get supply cap and enabled status")
    
    logger.info(f"  Morpho Contract ({config.MORPHO_ADDRESS[:10]}...):")
    logger.info(f"    - idToMarketParams(marketId) → Get market parameters")
    logger.info(f"    - market(marketId) → Get market state (supply/borrow)")
    logger.info(f"    - position(marketId, vaultAddr) → Get vault's position")
    
    # How data flows into strategy
    logger.info(f"\n🔄 Data Flow into Strategy:")
    logger.info(f"  1. Discover markets via vault.withdrawQueue")
    logger.info(f"  2. For each market, fetch params + state + position")
    logger.info(f"  3. Calculate current utilization = borrow_assets / supply_assets")
    logger.info(f"  4. Compare to target utilization from config")
    logger.info(f"  5. Determine if market needs more/less allocation")
    logger.info(f"  6. Generate reallocation actions in priority order")
    logger.info(f"  7. Build transaction to call vault.reallocate()")
    
    logger.info(f"\n✅ Data fetching is working correctly!")
    logger.info(f"   The vault currently has no assets allocated, but the")
    logger.info(f"   system is ready to manage allocations when funds are deposited.")

if __name__ == "__main__":
    main()
