#!/usr/bin/env python3
"""
Test our data module implementation without network dependencies.

This script tests that our implementation correctly follows the interface contracts
and can handle data transformation properly.
"""

import os
import sys
from log import get_logger

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logger = get_logger(__name__)


def test_interface_compliance():
    """Test that our implementations follow interface contracts."""
    logger.info("Testing interface compliance...")
    
    try:
        # Import interfaces
        from interfaces.market_interface import IMarketData
        from interfaces.vault_interface import IVaultData
        
        # Import our implementations
        from market_data import MarketData
        from vault_data import VaultData
        
        # Test MarketData
        test_market_id = "******************************************123456789012345678901234"
        market_data = MarketData(test_market_id, chain_id=8453)
        
        # Check inheritance
        assert isinstance(market_data, IMarketData), "MarketData must implement IMarketData"
        
        # Check methods exist
        assert hasattr(market_data, 'fetch_market_data'), "Missing fetch_market_data method"
        assert hasattr(market_data, 'fetch_market_params'), "Missing fetch_market_params method"
        assert hasattr(market_data, 'fetch_market_state'), "Missing fetch_market_state method"
        
        # Test VaultData
        test_vault_address = "******************************************"
        vault_data = VaultData(test_vault_address, chain_id=8453)
        
        # Check inheritance
        assert isinstance(vault_data, IVaultData), "VaultData must implement IVaultData"
        
        # Check methods exist
        assert hasattr(vault_data, 'fetch_vault_data'), "Missing fetch_vault_data method"
        
        logger.info("✅ Interface compliance test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Interface compliance test failed: {e}")
        return False


def test_data_transformation():
    """Test data transformation functions with mock data."""
    logger.info("Testing data transformation...")
    
    try:
        from graph_client.transformers import (
            transform_market_data,
            transform_market_params,
            transform_market_state,
            validate_market_data,
            validate_vault_data
        )
        
        # Mock GraphQL market data
        mock_market = {
            "id": "******************************************123456789012345678901234",
            "name": "Test Market",
            "borrowedToken": {"id": "0xtoken1", "symbol": "TOKEN1"},
            "collateralToken": {"id": "0xtoken2", "symbol": "TOKEN2"},
            "irm": "0xirm123",
            "oracle": "0xoracle123",
            "lltv": "860000000000000000",
            "totalSupply": "1000000000000000000000",
            "totalSupplyShares": "1000000000000000000000",
            "totalBorrow": "500000000000000000000",
            "totalBorrowShares": "500000000000000000000",
            "createdTimestamp": "1640995200",
            "fee": "100000000000000000"
        }
        
        # Test market data transformation
        market_data = transform_market_data(mock_market, 8453, "100000000000000000000", "2000000000000000000000", "50000000000000000")
        
        # Validate structure
        assert validate_market_data(market_data), "Market data validation failed"
        
        # Check required fields
        assert market_data["chain_id"] == "8453"
        assert market_data["id"] == mock_market["id"]
        assert "params" in market_data
        assert "state" in market_data
        
        # Test market params transformation
        params = transform_market_params(mock_market)
        assert params["loan_token"] == "0xtoken1"
        assert params["collateral_token"] == "0xtoken2"
        assert params["irm"] == "0xirm123"
        
        # Test market state transformation
        state = transform_market_state(mock_market)
        assert state["total_supply_assets"] == "1000000000000000000000"
        assert state["total_borrow_assets"] == "500000000000000000000"
        
        # Test vault data structure
        mock_vault_data = {
            "vault_address": "******************************************",
            "market_data": [market_data]
        }
        
        assert validate_vault_data(mock_vault_data), "Vault data validation failed"
        
        logger.info("✅ Data transformation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Data transformation test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    logger.info("Testing configuration...")
    
    try:
        from config.config import (
            THE_GRAPH_API_KEY,
            SUPPORTED_CHAINS,
            PRIMARY_CHAIN_ID,
            get_chain_config,
            get_query_url
        )
        
        # Check API key
        if not THE_GRAPH_API_KEY:
            logger.warning("⚠️ THE_GRAPH_API_KEY not set")
        else:
            logger.info(f"✅ API key configured: {THE_GRAPH_API_KEY[:10]}...")
        
        # Check chain config
        base_config = get_chain_config(8453)
        assert base_config is not None, "Base chain config not found"
        assert base_config["name"] == "Base", "Base chain name incorrect"
        
        # Check query URL generation
        if THE_GRAPH_API_KEY:
            query_url = get_query_url(8453)
            assert query_url is not None, "Query URL generation failed"
            assert "gateway.thegraph.com" in query_url, "Query URL format incorrect"
            logger.info(f"✅ Query URL: {query_url[:50]}...")
        
        logger.info("✅ Configuration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False


def test_graphql_client_creation():
    """Test GraphQL client can be created."""
    logger.info("Testing GraphQL client creation...")
    
    try:
        from graph_client.client import create_client, GraphQLClient
        
        # Test client creation
        client = create_client(8453)
        assert isinstance(client, GraphQLClient), "Client creation failed"
        
        # Test client info
        info = client.get_chain_info()
        assert info["chain_id"] == 8453, "Chain ID incorrect"
        assert info["chain_name"] == "Base", "Chain name incorrect"
        
        logger.info("✅ GraphQL client creation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ GraphQL client creation test failed: {e}")
        return False


def test_collectors_creation():
    """Test that collectors can be created."""
    logger.info("Testing collectors creation...")
    
    try:
        from graph_client.market_collector import create_market_collector
        from graph_client.vault_collector import create_vault_collector
        
        # Test market collector
        market_collector = create_market_collector(8453)
        assert market_collector is not None, "Market collector creation failed"
        
        # Test vault collector
        vault_collector = create_vault_collector(8453)
        assert vault_collector is not None, "Vault collector creation failed"
        
        logger.info("✅ Collectors creation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Collectors creation test failed: {e}")
        return False


def main():
    """Run implementation tests."""
    logger.info("🚀 Starting implementation tests...")
    
    tests = [
        ("Configuration", test_configuration),
        ("GraphQL Client Creation", test_graphql_client_creation),
        ("Collectors Creation", test_collectors_creation),
        ("Data Transformation", test_data_transformation),
        ("Interface Compliance", test_interface_compliance),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("IMPLEMENTATION TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("🎉 All implementation tests passed!")
        logger.info("✅ Data module is correctly implemented and follows interface contracts!")
        return 0
    else:
        logger.error("💥 Some implementation tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
