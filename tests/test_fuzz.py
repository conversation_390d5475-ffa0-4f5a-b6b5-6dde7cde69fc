#!/usr/bin/env python3
"""
Fuzz Testing for Morpho Reallocator Bot Strategy

Uses Hypothesis for property-based testing to ensure the strategy is robust
against all possible input configurations.

Install: pip install hypothesis
Run: python tests/test_fuzz.py
"""

import unittest
import sys
import os
from decimal import Decimal

# Add root directory and src to path
root_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, root_dir)
sys.path.insert(0, os.path.join(root_dir, 'src'))

try:
    from hypothesis import given, strategies as st, assume, settings
    from hypothesis.strategies import composite
    HYPOTHESIS_AVAILABLE = True
except ImportError:
    HYPOTHESIS_AVAILABLE = False
    print("❌ Hypothesis not installed. Run 'pip install hypothesis' to enable fuzz tests.")
    sys.exit(1)

from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
from strategies.equalize_util.equalize_util import EqualizeUtil
import config

# Import test utilities from main test file
from test_strategy import TestDataFactory, MockVaultData


class FuzzTestStrategy(unittest.TestCase):
    """Comprehensive fuzz testing for strategy robustness."""
    
    def setUp(self):
        self.strategy = PrioritizedUtilizationStrategy()
        self.factory = TestDataFactory()
    
    @composite
    def realistic_market_data(draw):
        """Generate realistic market data for fuzz testing."""
        # Use real market IDs from the vault
        market_id = draw(st.sampled_from(TestDataFactory.MARKET_IDS))
        
        # Realistic supply amounts (1K to 10B USDC)
        supply_usdc = draw(st.floats(
            min_value=0.001, 
            max_value=10000, 
            allow_nan=False, 
            allow_infinity=False
        ))
        
        # Borrow should be <= supply, realistic utilization 0-99.9%
        max_borrow = supply_usdc * 0.999
        borrow_usdc = draw(st.floats(
            min_value=0, 
            max_value=max_borrow, 
            allow_nan=False, 
            allow_infinity=False
        ))
        
        # Vault assets: 0 to reasonable amount (up to 20% of supply)
        max_vault = supply_usdc * 0.2
        vault_usdc = draw(st.floats(
            min_value=0, 
            max_value=max_vault, 
            allow_nan=False, 
            allow_infinity=False
        ))
        
        # Supply cap: should be >= current supply
        cap_usdc = draw(st.floats(
            min_value=supply_usdc, 
            max_value=supply_usdc * 100, 
            allow_nan=False, 
            allow_infinity=False
        ))
        
        return market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc
    
    @given(st.lists(realistic_market_data(), min_size=1, max_size=12, unique_by=lambda x: x[0]))
    @settings(max_examples=100, deadline=10000)
    def test_strategy_never_crashes(self, market_configs):
        """Property: Strategy should never crash regardless of valid input."""
        try:
            markets = []
            for market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc in market_configs:
                # Skip mathematically invalid configurations
                assume(supply_usdc > 0)
                assume(borrow_usdc >= 0)
                assume(vault_usdc >= 0)
                assume(borrow_usdc <= supply_usdc)
                assume(cap_usdc >= supply_usdc)
                
                markets.append(self.factory.create_mock_market(
                    market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc
                ))
            
            vault_data = MockVaultData(markets)
            
            # The critical test: strategy should never crash
            result = self.strategy.find_reallocation(vault_data)
            
            # Basic sanity checks if result exists
            if result is not None:
                self.assertIsInstance(result, list)
                for action in result:
                    self.assertIsInstance(action, tuple)
                    self.assertEqual(len(action), 2)
                    params, new_amount = action
                    self.assertGreaterEqual(new_amount, 0, "Allocations should be non-negative")
                    
        except Exception as e:
            self.fail(f"Strategy crashed with input: {e}")
    
    @given(st.lists(realistic_market_data(), min_size=2, max_size=8, unique_by=lambda x: x[0]))
    @settings(max_examples=50)
    def test_fund_conservation_property(self, market_configs):
        """Property: Total vault assets should always be conserved.

        Note: Strategy only returns actions for markets that change.
        We must account for unchanged markets to properly test conservation.
        """
        try:
            markets = []
            total_before = 0
            
            for market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc in market_configs:
                assume(supply_usdc > 0 and borrow_usdc >= 0 and vault_usdc >= 0)
                assume(borrow_usdc <= supply_usdc)
                assume(cap_usdc >= supply_usdc)
                
                markets.append(self.factory.create_mock_market(
                    market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc
                ))
                total_before += vault_usdc
            
            # Only test conservation when there are assets to conserve
            assume(total_before > 0.001)  # At least 1 cent

            vault_data = MockVaultData(markets)
            result = self.strategy.find_reallocation(vault_data)

            if result is not None and len(result) > 0:
                # CORRECT LOGIC: Strategy returns actions for all affected markets
                # The result contains (params, new_amount) pairs
                # We need to sum all the new amounts to get total after

                # IMPORTANT: Convert from internal representation (wei) back to USDC
                USDC_SCALE = 1e6  # USDC has 6 decimals
                total_after = sum(float(new_amount) / USDC_SCALE for params, new_amount in result)

                # Fund conservation: total should be preserved
                delta = max(0.01, total_before * 0.0001)  # 0.01% or 1 cent, whichever is larger

                if abs(total_before - total_after) > delta:
                    # Print detailed info for debugging
                    print(f"\n🚨 FUND CONSERVATION VIOLATION DETECTED:")
                    print(f"   Before: {total_before:.6f} USDC")
                    print(f"   After:  {total_after:.6f} USDC")
                    print(f"   Diff:   {abs(total_before - total_after):.6f} USDC")
                    print(f"   Markets: {len(markets)}")
                    print(f"   Actions: {len(result)}")
                    for i, market in enumerate(markets):
                        original_usdc = float(market.data["vault_assets"]) / USDC_SCALE
                        print(f"     Market {i} (original): {original_usdc:.6f} USDC")
                    for i, (params, new_amount) in enumerate(result):
                        new_amount_usdc = float(new_amount) / USDC_SCALE
                        print(f"     Action {i} (result): {new_amount_usdc:.6f} USDC")

                self.assertAlmostEqual(
                    total_before, total_after,
                    delta=delta,
                    msg=f"Fund conservation violated: {total_before:.6f} -> {total_after:.6f}"
                )
                
        except Exception as e:
            # Don't fail on assumption violations - they're expected
            if "UnsatisfiedAssumption" in str(type(e)):
                return  # Skip this test case
            self.fail(f"Fund conservation test failed: {e}")
    
    @given(st.lists(realistic_market_data(), min_size=1, max_size=6, unique_by=lambda x: x[0]))
    @settings(max_examples=30)
    def test_output_structure_property(self, market_configs):
        """Property: Output should always have valid structure when not None."""
        try:
            markets = []
            
            for market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc in market_configs:
                assume(supply_usdc > 0 and borrow_usdc >= 0 and vault_usdc >= 0)
                assume(borrow_usdc <= supply_usdc)
                assume(cap_usdc >= supply_usdc)
                
                markets.append(self.factory.create_mock_market(
                    market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc
                ))
            
            vault_data = MockVaultData(markets)
            result = self.strategy.find_reallocation(vault_data)
            
            if result is not None:
                # Should be a list
                self.assertIsInstance(result, list)
                
                # Should not have more actions than markets
                self.assertLessEqual(len(result), len(markets))
                
                # Each action should be properly structured
                for action in result:
                    self.assertIsInstance(action, tuple)
                    self.assertEqual(len(action), 2)
                    params, new_amount = action
                    
                    # New amount should be a valid number
                    self.assertIsInstance(new_amount, (int, float, Decimal))
                    self.assertGreaterEqual(new_amount, 0)
                    self.assertFalse(str(new_amount).lower() in ['nan', 'inf', '-inf'])
                
        except Exception as e:
            self.fail(f"Output structure test failed: {e}")

    @given(st.lists(realistic_market_data(), min_size=1, max_size=6, unique_by=lambda x: x[0]))
    @settings(max_examples=50)
    def test_never_allocates_to_idle_markets(self, market_configs):
        """Property: Strategy should NEVER allocate funds to Idle markets.

        Idle markets are identified by having 100% target utilization.
        They should never receive positive supply asset changes.
        """
        try:
            markets = []
            idle_market_ids = set()

            for market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc in market_configs:
                assume(supply_usdc > 0 and borrow_usdc >= 0 and vault_usdc >= 0)
                assume(borrow_usdc <= supply_usdc)
                assume(cap_usdc >= supply_usdc)

                market = self.factory.create_mock_market(
                    market_id, supply_usdc, borrow_usdc, vault_usdc, cap_usdc
                )
                markets.append(market)

                # Check if this is an Idle market (100% target utilization)
                target = config.config.target_util.get(market_id, 0)
                if target >= config.config.WAD:  # 100% or higher = Idle market
                    idle_market_ids.add(market_id)

            # Skip test if no idle markets present
            if not idle_market_ids:
                return

            vault_data = MockVaultData(markets)
            result = self.strategy.find_reallocation(vault_data)

            if result is not None and len(result) > 0:
                # Check each action to ensure no Idle market receives positive allocation
                for params, new_amount in result:
                    # Find the market this action applies to
                    market_id = None
                    original_amount = 0

                    for market in markets:
                        if (market.params["loan_token"] == params["loan_token"] and
                            market.params["collateral_token"] == params["collateral_token"] and
                            market.params["oracle"] == params["oracle"] and
                            market.params["irm"] == params["irm"] and
                            market.params["lltv"] == params["lltv"]):
                            market_id = market.data["id"]
                            original_amount = market.data["vault_assets"]
                            break

                    if market_id and market_id in idle_market_ids:
                        # This is an Idle market - check for violations
                        change = float(new_amount) - float(original_amount)

                        if change > 0.01:  # Allow for tiny rounding errors
                            # Print detailed violation info
                            print(f"\n🚨 IDLE MARKET ALLOCATION VIOLATION DETECTED:")
                            print(f"   Market ID: {market_id}")
                            print(f"   Target Utilization: 100% (Idle market)")
                            print(f"   Original Amount: {original_amount / 1e6:.6f} USDC")
                            print(f"   New Amount: {new_amount / 1e6:.6f} USDC")
                            print(f"   Change: +{change / 1e6:.6f} USDC (VIOLATION!)")
                            print(f"   Total Markets: {len(markets)}")
                            print(f"   Idle Markets: {len(idle_market_ids)}")

                            self.fail(
                                f"Strategy allocated {change / 1e6:.6f} USDC to Idle market {market_id}. "
                                f"Idle markets should NEVER receive positive allocations!"
                            )

        except Exception as e:
            self.fail(f"Idle market test failed with error: {e}")

    def test_specific_idle_market_violation(self):
        """Specific test case that should catch Idle market allocation violations.

        Recreates the exact scenario from main test suite that shows violations.
        """
        # Create the exact scenario that triggers violations: 12 markets with mixed utilizations
        markets = []

        # Market configurations that trigger the violation
        market_configs = [
            (self.factory.MARKET_IDS[0], 1000000, 850000, 100000, 1000000),   # Idle market - 85% util vs 100% target
            (self.factory.MARKET_IDS[1], 1000000, 860000, 150000, 1000000),   # Market 2 - 86% util vs 95% target
            (self.factory.MARKET_IDS[2], 1000000, 870000, 200000, 1000000),   # Market 3 - 87% util vs 91% target
            (self.factory.MARKET_IDS[3], 1000000, 880000, 250000, 1000000),   # Market 4 - 88% util vs 90% target
            (self.factory.MARKET_IDS[4], 1000000, 890000, 300000, 1000000),   # Market 5 - 89% util vs 90% target
            (self.factory.MARKET_IDS[5], 1000000, 900000, 350000, 1000000),   # Market 6 - 90% util vs 100% target
            (self.factory.MARKET_IDS[6], 1000000, 910000, 400000, 1000000),   # Market 7 - 91% util vs 90% target (OVER)
            (self.factory.MARKET_IDS[7], 1000000, 920000, 450000, 1000000),   # Market 8 - 92% util vs 90% target (OVER)
            (self.factory.MARKET_IDS[8], 1000000, 930000, 500000, 1000000),   # Market 9 - 93% util vs 89% target (OVER)
            (self.factory.MARKET_IDS[9], 1000000, 940000, 550000, 1000000),   # Market 10 - 94% util vs 91% target (OVER)
            (self.factory.MARKET_IDS[10], 1000000, 950000, 600000, 1000000),  # Market 11 - 95% util vs 90% target (OVER)
            (self.factory.MARKET_IDS[11], 1000000, 960000, 650000, 1000000),  # Market 12 - 96% util vs 90% target (OVER)
        ]

        for market_id, supply, borrow, vault, cap in market_configs:
            market = self.factory.create_mock_market(market_id, supply, borrow, vault, cap)
            markets.append(market)

        vault_data = MockVaultData(markets)
        result = self.strategy.find_reallocation(vault_data)

        # This scenario SHOULD trigger violations
        self.assertIsNotNone(result, "Strategy should return reallocation actions for this scenario")
        self.assertGreater(len(result), 0, "Strategy should return multiple actions")

        print(f"\n🔍 IDLE MARKET VIOLATION TEST:")
        print(f"   Strategy returned {len(result)} actions")

        idle_market_id = self.factory.MARKET_IDS[0]  # First market is Idle
        violations_found = 0

        # Check each action for Idle market violations
        for i, (params, new_amount) in enumerate(result):
            # Find the market this action applies to
            market_id = None
            original_amount = 0

            for market in markets:
                if (market.params["loan_token"] == params["loan_token"] and
                    market.params["collateral_token"] == params["collateral_token"] and
                    market.params["oracle"] == params["oracle"] and
                    market.params["irm"] == params["irm"] and
                    market.params["lltv"] == params["lltv"]):
                    market_id = market.data["id"]
                    original_amount = market.data["vault_assets"]
                    break

            if market_id == idle_market_id:
                change = float(new_amount) - float(original_amount)
                print(f"   Action {i+1}: IDLE market {original_amount/1e6:.0f} → {new_amount/1e6:.0f} USDC (change: {change/1e6:+.0f})")

                if change > 0.01:  # Positive allocation to Idle market
                    violations_found += 1

        if violations_found > 0:
            self.fail(
                f"🚨 CRITICAL BUG: Strategy allocated funds to Idle market in {violations_found} actions! "
                f"Idle markets should NEVER receive positive allocations."
            )


def run_fuzz_tests():
    """Run fuzz tests with detailed output."""
    print("🧪 MORPHO REALLOCATOR BOT - FUZZ TESTING")
    print("=" * 60)
    print("Testing strategy robustness with random inputs...")
    print("This may take a few minutes...")
    print("=" * 60)
    
    # Run the tests
    suite = unittest.TestLoader().loadTestsFromTestCase(FuzzTestStrategy)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All fuzz tests passed! Strategy is robust.")
        print("   - No crashes with random inputs")
        print("   - Fund conservation maintained")
        print("   - Output structure always valid")
        print("   - Never allocates to Idle markets")
    else:
        print("❌ Some fuzz tests failed!")
        print("   Check the output above for details.")
    print("=" * 60)
    
    return result.wasSuccessful()


def debug_failing_case():
    """Debug the specific failing case from fuzz testing."""
    print("🔍 DEBUGGING SPECIFIC FAILING CASE")
    print("=" * 60)

    # Import required classes
    from tests.test_strategy import TestDataFactory, MockVaultData
    from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
    from utils.math import get_utilization
    from config import config

    # Recreate the exact failing case
    market_configs = [
        ('0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81', 5.0, 1.0, 1.0, 5.0),  # PT-USR Sept
        ('0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb', 1.0, 0.0, 0.0, 1.0),   # Idle market
        ('0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836', 2497.0, 2494.0, 0.0, 2499.0)  # cbBTC
    ]

    # Create markets
    factory = TestDataFactory()
    markets = []
    for market_id, supply, borrow, vault, cap in market_configs:
        market = factory.create_mock_market(market_id, supply, borrow, vault, cap)
        markets.append(market)

    # Create vault data and run strategy
    vault_data = MockVaultData(markets)
    strategy = PrioritizedUtilizationStrategy()
    result = strategy.find_reallocation(vault_data)

    print(f"📊 INPUT ANALYSIS:")
    total_vault_assets = 0
    for i, market in enumerate(markets):
        vault_assets = market.data["vault_assets"] / 1e6  # Convert to USDC
        total_vault_assets += vault_assets
        utilization = get_utilization(market.data["state"])
        target = config.target_util.get(market.data["id"], 0)
        target_pct = (target / config.WAD) * 100 if target else 0
        util_pct = (utilization / config.WAD) * 100 if utilization else 0

        print(f"  Market {i+1}: {vault_assets:.6f} USDC, {util_pct:.1f}% util (target: {target_pct:.1f}%)")

    print(f"  💰 Total Vault Assets: {total_vault_assets:.6f} USDC")

    print(f"\n🤖 STRATEGY RESULT:")
    if result is None:
        print("  Strategy returned None (no reallocation)")
    else:
        print(f"  Strategy returned {len(result)} actions:")
        total_after = 0
        for i, (params, new_amount) in enumerate(result):
            new_amount_usdc = new_amount / 1e6
            total_after += new_amount_usdc
            print(f"    Action {i+1}: {new_amount_usdc:.6f} USDC")

        print(f"  💰 Total After: {total_after:.6f} USDC")
        print(f"  🚨 Conservation: {total_vault_assets:.6f} → {total_after:.6f} USDC")

        if abs(total_vault_assets - total_after) > 0.01:
            print("  ❌ FUND CONSERVATION VIOLATED!")
        else:
            print("  ✅ Fund conservation OK")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "debug":
        debug_failing_case()
    else:
        if not HYPOTHESIS_AVAILABLE:
            print("Install Hypothesis first: pip install hypothesis")
            sys.exit(1)

        success = run_fuzz_tests()
        sys.exit(0 if success else 1)
