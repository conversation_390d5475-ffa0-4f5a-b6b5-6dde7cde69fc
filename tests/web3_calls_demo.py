#!/usr/bin/env python3
"""
Web3 Calls Demonstration

Shows the exact Web3 contract calls being made by the bot.
"""

import sys
import os

# Add root directory and src to path
root_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, root_dir)
sys.path.insert(0, os.path.join(root_dir, 'src'))

from log import get_logger

logger = get_logger(__name__)

def demonstrate_web3_calls():
    """Demonstrate the exact Web3 calls made by the bot."""
    from utils.web3_connector import Web3Connector
    from utils.web3_contract import Web3Contract
    from config import config
    
    logger.info("🌐 WEB3 CALLS DEMONSTRATION")
    logger.info("=" * 50)
    
    # Connect to Web3
    logger.info(f"Connecting to: {config.PRIMARY_RPC_URL}")
    client = Web3Connector(provider_url=config.PRIMARY_RPC_URL).get_client()
    logger.info(f"✅ Connected: {client.is_connected()}")
    logger.info(f"📦 Latest block: {client.eth.block_number:,}")
    
    # Initialize contracts
    vault_contract = Web3Contract(client, config.VAULT_ADDRESS, config.VAULT_ABI)
    morpho_contract = Web3Contract(client, config.MORPHO_ADDRESS, config.MORPHO_ABI)
    
    logger.info(f"\n🏦 VAULT CONTRACT: {config.VAULT_ADDRESS}")
    logger.info("-" * 50)
    
    # Step 1: Get number of markets
    logger.info("📞 Call: withdrawQueueLength()")
    queue_length = vault_contract.call("withdrawQueueLength")
    logger.info(f"📋 Result: {queue_length} markets in vault")
    
    # Step 2: Get first few market IDs
    logger.info(f"\n📞 Calls: withdrawQueue(i) for i=0 to {min(3, queue_length-1)}")
    market_ids = []
    for i in range(min(3, queue_length)):
        market_id = vault_contract.call("withdrawQueue", [i])
        market_ids.append(market_id)
        logger.info(f"📋 withdrawQueue({i}) → {market_id.hex()}")
    
    # Step 3: Get market configs
    logger.info(f"\n📞 Calls: config(marketId) for each market")
    for i, market_id in enumerate(market_ids):
        config_result = vault_contract.call("config", [market_id])
        cap, enabled, removable_at = config_result
        logger.info(f"📋 config({market_id.hex()[:10]}...)")
        logger.info(f"    Supply Cap: {cap:,} ({cap/1e6:.2f}M USDC)")
        logger.info(f"    Enabled: {enabled}")
        logger.info(f"    Removable At: {removable_at}")
    
    logger.info(f"\n🔷 MORPHO CONTRACT: {config.MORPHO_ADDRESS}")
    logger.info("-" * 50)
    
    # Step 4: Get market parameters
    logger.info(f"📞 Calls: idToMarketParams(marketId) for each market")
    for i, market_id in enumerate(market_ids):
        params = morpho_contract.call("idToMarketParams", [market_id])
        loan_token, collateral_token, oracle, irm, lltv = params
        logger.info(f"📋 idToMarketParams({market_id.hex()[:10]}...)")
        logger.info(f"    Loan Token: {loan_token}")
        logger.info(f"    Collateral: {collateral_token}")
        logger.info(f"    Oracle: {oracle}")
        logger.info(f"    IRM: {irm}")
        logger.info(f"    LLTV: {lltv/1e18:.1%}")
    
    # Step 5: Get market state
    logger.info(f"\n📞 Calls: market(marketId) for each market")
    for i, market_id in enumerate(market_ids):
        state = morpho_contract.call("market", [market_id])
        supply_assets, supply_shares, borrow_assets, borrow_shares, last_update, fee = state
        utilization = (borrow_assets / supply_assets * 100) if supply_assets > 0 else 0
        logger.info(f"📋 market({market_id.hex()[:10]}...)")
        logger.info(f"    Supply: {supply_assets:,} assets, {supply_shares:,} shares")
        logger.info(f"    Borrow: {borrow_assets:,} assets, {borrow_shares:,} shares")
        logger.info(f"    Utilization: {utilization:.1f}%")
        logger.info(f"    Last Update: {last_update}")
        logger.info(f"    Fee: {fee}")
    
    # Step 6: Get vault positions
    logger.info(f"\n📞 Calls: position(marketId, vaultAddress) for each market")
    for i, market_id in enumerate(market_ids):
        position = morpho_contract.call("position", [market_id, config.VAULT_ADDRESS])
        supply_shares, borrow_shares, collateral = position
        
        # Calculate vault assets
        if supply_shares > 0:
            market_state = morpho_contract.call("market", [market_id])
            total_supply_assets, total_supply_shares = market_state[0], market_state[1]
            vault_assets = (supply_shares * total_supply_assets) // total_supply_shares if total_supply_shares > 0 else 0
        else:
            vault_assets = 0
            
        logger.info(f"📋 position({market_id.hex()[:10]}..., vault)")
        logger.info(f"    Supply Shares: {supply_shares:,}")
        logger.info(f"    Vault Assets: {vault_assets:,} ({vault_assets/1e6:.2f} USDC)")
        logger.info(f"    Borrow Shares: {borrow_shares}")
        logger.info(f"    Collateral: {collateral}")
    
    logger.info(f"\n✅ SUMMARY")
    logger.info("-" * 50)
    logger.info(f"Total Web3 calls made: {3 + len(market_ids) * 4}")
    logger.info(f"  - 1 call to get queue length")
    logger.info(f"  - {len(market_ids)} calls to get market IDs")
    logger.info(f"  - {len(market_ids)} calls to get market configs")
    logger.info(f"  - {len(market_ids)} calls to get market params")
    logger.info(f"  - {len(market_ids)} calls to get market state")
    logger.info(f"  - {len(market_ids)} calls to get vault positions")
    logger.info(f"\nFor full vault with {queue_length} markets:")
    logger.info(f"  Total calls would be: {1 + queue_length * 4}")

def main():
    """Main demonstration function."""
    try:
        demonstrate_web3_calls()
        logger.info(f"\n🎉 Web3 calls demonstration complete!")
        return 0
    except Exception as e:
        logger.error(f"❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
