#!/usr/bin/env python3
"""
Test script to verify our implementation works with the new interface changes.

The vault interface now expects MarketData objects instead of dictionaries.
"""

import os
import sys
from log import get_logger

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logger = get_logger(__name__)


def test_new_interface_compliance():
    """Test that our implementation follows the new interface requirements."""
    logger.info("🔍 Testing new interface compliance...")
    
    try:
        # Import interfaces and implementations
        from interfaces.market_interface import IMarketData
        from interfaces.vault_interface import IVaultData
        from market_data import MarketData
        from vault_data import VaultData
        
        # Test MarketData (should still work the same)
        test_market_id = "0x1234567890123456789012345678901234567890123456789012345678901234"
        market_data = MarketData(test_market_id, chain_id=8453)
        
        # Check inheritance
        assert isinstance(market_data, IMarketData), "MarketData must implement IMarketData"
        logger.info("✅ MarketData interface compliance maintained")
        
        # Test VaultData (this is where the change is)
        test_vault_address = "0x1234567890123456789012345678901234567890"
        vault_data = VaultData(test_vault_address, chain_id=8453)
        
        # Check inheritance
        assert isinstance(vault_data, IVaultData), "VaultData must implement IVaultData"
        logger.info("✅ VaultData interface compliance maintained")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Interface compliance test failed: {e}")
        return False


def test_vault_data_structure():
    """Test that vault data returns MarketData objects as expected."""
    logger.info("🔍 Testing vault data structure...")
    
    try:
        from market_data import MarketData
        from vault_data import VaultData
        
        # Create a vault data instance
        test_vault_address = "0x1234567890123456789012345678901234567890"
        vault_data = VaultData(test_vault_address, chain_id=8453)
        
        # Mock the collector to return test data
        class MockVaultCollector:
            def fetch_vault_data(self, vault_address):
                return {
                    "vault_address": vault_address,
                    "market_data": [
                        {
                            "chain_id": "8453",
                            "id": "0xmarket1",
                            "params": {
                                "loan_token": "0xtoken1",
                                "collateral_token": "0xtoken2",
                                "irm": "0xirm",
                                "oracle": "0xoracle",
                                "lltv": "860000000000000000",
                            },
                            "state": {
                                "total_supply_assets": "1000000000000000000000",
                                "total_supply_shares": "1000000000000000000000",
                                "total_borrow_assets": "500000000000000000000",
                                "total_borrow_shares": "500000000000000000000",
                                "last_update": "1640995200",
                                "fee": "100000000000000000",
                            },
                            "cap": "2000000000000000000000",
                            "vault_assets": "100000000000000000000",
                            "rate_at_target": "50000000000000000",
                        }
                    ]
                }
        
        # Replace the collector with our mock
        vault_data.collector = MockVaultCollector()
        
        # Fetch vault data
        result = vault_data.fetch_vault_data(test_vault_address)
        
        # Verify structure
        assert "vault_address" in result, "Missing vault_address"
        assert "market_data" in result, "Missing market_data"
        assert isinstance(result["market_data"], list), "market_data must be a list"
        
        # Verify MarketData objects
        market_data_list = result["market_data"]
        assert len(market_data_list) > 0, "Should have at least one market"
        
        for market_obj in market_data_list:
            assert isinstance(market_obj, MarketData), f"Expected MarketData object, got {type(market_obj)}"
            logger.info(f"✅ Found MarketData object for market: {market_obj.market_id}")
        
        logger.info("✅ Vault data structure test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Vault data structure test failed: {e}")
        return False


def test_market_data_object_functionality():
    """Test that MarketData objects work correctly within vault data."""
    logger.info("🔍 Testing MarketData object functionality...")
    
    try:
        from market_data import MarketData
        
        # Create a MarketData object
        market_id = "0xmarket1"
        market_obj = MarketData(market_id, chain_id=8453)
        
        # Pre-populate cache (simulating what VaultData does)
        test_data = {
            "chain_id": "8453",
            "id": market_id,
            "params": {
                "loan_token": "0xtoken1",
                "collateral_token": "0xtoken2",
                "irm": "0xirm",
                "oracle": "0xoracle",
                "lltv": "860000000000000000",
            },
            "state": {
                "total_supply_assets": "1000000000000000000000",
                "total_supply_shares": "1000000000000000000000",
                "total_borrow_assets": "500000000000000000000",
                "total_borrow_shares": "500000000000000000000",
                "last_update": "1640995200",
                "fee": "100000000000000000",
            },
            "cap": "2000000000000000000000",
            "vault_assets": "100000000000000000000",
            "rate_at_target": "50000000000000000",
        }
        
        market_obj._data_cache = test_data
        market_obj._params_cache = test_data["params"]
        market_obj._state_cache = test_data["state"]
        
        # Test that cached data can be retrieved
        retrieved_data = market_obj.fetch_market_data(market_id)
        assert retrieved_data == test_data, "Cached data retrieval failed"
        
        retrieved_params = market_obj.fetch_market_params(market_id)
        assert retrieved_params == test_data["params"], "Cached params retrieval failed"
        
        retrieved_state = market_obj.fetch_market_state(market_id)
        assert retrieved_state == test_data["state"], "Cached state retrieval failed"
        
        logger.info("✅ MarketData object functionality test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ MarketData object functionality test failed: {e}")
        return False


def main():
    """Run interface change tests."""
    logger.info("🚀 Testing Interface Changes Compliance")
    
    tests = [
        ("New Interface Compliance", test_new_interface_compliance),
        ("Vault Data Structure", test_vault_data_structure),
        ("MarketData Object Functionality", test_market_data_object_functionality),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("INTERFACE CHANGES TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("🎉 All interface change tests passed!")
        logger.info("✅ Implementation updated to comply with new interface!")
        return 0
    else:
        logger.error("💥 Some interface change tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
