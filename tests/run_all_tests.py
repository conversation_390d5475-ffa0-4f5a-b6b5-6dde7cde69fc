#!/usr/bin/env python3
"""
Run all tests for the Morpho Reallocator Bot.

This script runs both the main strategy tests and fuzz tests,
providing a comprehensive test suite for the bot.
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, cwd=".", capture_output=False)
        print(f"✅ {description} - PASSED")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED (exit code: {e.returncode})")
        return False
    except FileNotFoundError:
        print(f"❌ {description} - FAILED (command not found)")
        return False

def check_hypothesis():
    """Check if Hypothesis is installed for fuzz testing."""
    try:
        import hypothesis
        return True
    except ImportError:
        return False

def main():
    """Run all test suites."""
    print("🚀 MORPHO REALLOCATOR BOT - COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    
    results = []
    
    # 1. Run main strategy tests
    results.append(run_command(
        [sys.executable, "tests/test_strategy.py", "-v"],
        "Main Strategy Tests"
    ))
    
    # 2. Run allocation sanity check
    results.append(run_command(
        [sys.executable, "tests/run_allocation_tests.py"],
        "Allocation Sanity Check"
    ))
    
    # 3. Run fuzz tests if Hypothesis is available
    if check_hypothesis():
        results.append(run_command(
            [sys.executable, "tests/test_fuzz.py"],
            "Fuzz Tests (Robustness)"
        ))
    else:
        print(f"\n{'='*60}")
        print("⚠️  Fuzz Tests - SKIPPED")
        print("   Hypothesis not installed. Run: pip install hypothesis")
        print(f"{'='*60}")
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("🎉 Strategy is ready for production!")
        print("\nKey validations:")
        print("   ✓ Strategy logic works correctly")
        print("   ✓ Allocation decisions are sensible")
        print("   ✓ Priority order is respected")
        if check_hypothesis():
            print("   ✓ Robustness verified with fuzz testing")
        else:
            print("   ⚠ Fuzz testing skipped (install Hypothesis)")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total} passed)")
        print("🔧 Fix failing tests before deployment")
    
    print(f"{'='*80}")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
