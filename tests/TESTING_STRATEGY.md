# Testing Strategy for Morpho Reallocator Bot

## Overview

This document outlines the comprehensive testing strategy for the Morpho Reallocator Bot, focusing on edge cases and ensuring robust behavior under all conditions.

## Test Structure

### **Core Test Files**
- `test_strategy.py` - Main strategy tests with allocation output
- `test_fuzz.py` - Fuzz testing for robustness (requires Hypothesis)
- `TESTING_STRATEGY.md` - This documentation
- `FUZZ_TESTING.md` - Fuzz testing documentation
- `run_allocation_tests.py` - Comprehensive test runner

## Testing Philosophy

### 1. **Mock Data Based on Real Patterns**
- All mock data is based on actual observations from the live Morpho vault
- Uses realistic market sizes, utilization rates, and vault allocations
- Maintains proper scaling (USDC 6 decimals, shares 18 decimals)

### 2. **Edge Case Coverage**
- Test extreme scenarios that might not occur in normal operation
- Ensure graceful handling of error conditions
- Validate boundary conditions and mathematical edge cases

### 3. **Strategy Behavior Validation**
- Verify that reallocation decisions align with configured targets
- Ensure priority ordering is respected
- Confirm that constraints (caps, minimums) are honored

## Test Categories

### Core Functionality Tests
1. **Balanced Markets** - No reallocation needed when all markets are at target
2. **Simple Reallocation** - Basic over/under target scenarios
3. **Priority Ordering** - Verify markets are processed in correct priority order

### Priority-Based Scenarios (Critical for Production)
1. **Overall High Utilization** - All markets over target, priority-based withdrawal
2. **Overall Low Utilization** - All markets under target, priority-based deposit
3. **Mixed Utilization** - Some over, some under target with priority rebalancing
4. **Limited Funds Priority** - Priority order respected when funds are constrained
5. **Withdrawal Priority** - Lower priority markets provide funds first
6. **Realistic Mixed Scenario** - Complex priority-based reallocation

### Edge Case Tests
1. **Empty Vault** - No assets allocated to any market
2. **Extreme Utilization** - Very high (99.9%) and very low (0-10%) utilization
3. **Zero Supply Markets** - Markets with minimal or no supply
4. **Unknown Markets** - Markets not in priority configuration
5. **Large Scale** - Many markets with large amounts

### Constraint Tests
1. **Supply Caps** - Ensure allocations don't exceed market caps
2. **Mathematical Limits** - Division by zero, overflow scenarios
3. **Data Type Handling** - Proper integer/decimal handling

## Mock Data Design

### Realistic Market Parameters
```python
# Based on real Morpho markets
MARKET_IDS = [
    "0x669b68ae...",  # PT-USR Sept (Priority 1)
    "0x2c10e050...",  # PT-cUSDO Jul (Priority 2)  
    "0x1478d70d...",  # RLP (Priority 3)
    # ... more real market IDs
]

# Realistic market sizes (observed from live data)
supply_usdc = 1000000    # 1M USDC typical market size
borrow_usdc = 910000     # 91% utilization (common target)
vault_usdc = 50000       # 50K USDC typical vault allocation
```

### Scaling and Data Types
- **USDC amounts**: Scaled by 1e6 (6 decimals)
- **Share amounts**: Scaled by 1e18 (18 decimals)  
- **All values**: Converted to integers to match contract behavior
- **Utilization targets**: Use actual configured targets from config.py

## Test Scenarios

### 1. Normal Operation Scenarios
- **Balanced Portfolio**: All markets at target utilization
- **Minor Rebalancing**: Small deviations requiring modest adjustments
- **Major Rebalancing**: Significant imbalances requiring large moves

### 2. Stress Test Scenarios
- **Extreme Imbalance**: One market at 99.9%, another at 10%
- **Empty Vault**: No assets to reallocate
- **Single Market**: Only one market with all assets
- **Many Markets**: 10+ markets with complex interactions

### 3. Error Condition Scenarios
- **Division by Zero**: Markets with zero supply
- **Unknown Markets**: Markets not in priority list
- **Invalid Data**: Malformed market data
- **Constraint Violations**: Allocations exceeding caps

### 4. Boundary Condition Scenarios
- **Minimum Amounts**: Very small allocations (1 USDC)
- **Maximum Amounts**: Very large allocations (100M+ USDC)
- **Precision Limits**: Testing decimal precision boundaries
- **Cap Limits**: Allocations at exactly the supply cap

## Expected Behaviors

### Strategy Should:
1. **Return None** when no beneficial reallocation is possible
2. **Respect Priority Order** when processing markets
3. **Honor Supply Caps** and never exceed them
4. **Handle Edge Cases** gracefully without crashing
5. **Maintain Precision** in calculations
6. **Be Deterministic** - same inputs produce same outputs

### Strategy Should NOT:
1. **Crash** on invalid or edge case data
2. **Exceed Caps** or violate constraints
3. **Ignore Priority** ordering
4. **Make Unnecessary** reallocations
5. **Lose Precision** in calculations

## Test Data Validation

### Mock Data Must:
- Use **real market IDs** from the live vault
- Have **realistic proportions** (supply/borrow/vault ratios)
- Use **proper data types** (integers for contract values)
- Include **actual target utilizations** from config
- Maintain **mathematical consistency** (shares vs assets)

### Test Assertions Should:
- **Verify return types** (None or list of tuples)
- **Check constraint compliance** (caps, minimums)
- **Validate mathematical correctness** (no overflows)
- **Confirm behavioral expectations** (priority ordering)

## Running Strategy Tests

```bash
# Run all strategy tests (main test suite)
python tests/test_strategy.py -v

# Run specific test
python tests/test_strategy.py TestPrioritizedUtilizationStrategy.test_extreme_utilization_scenarios

# Run with buffer output (shows print statements immediately)
python tests/test_strategy.py -v -b

# Run with failfast (stop on first failure)
python tests/test_strategy.py -v -f

# Run fuzz tests (requires: pip install hypothesis)
python tests/test_fuzz.py

# Run comprehensive test suite (all tests with allocation details)
python tests/run_allocation_tests.py
```

**Note**: These tests use Python's built-in `unittest` framework, not `pytest`. If you prefer pytest-style output and options, you can install pytest and run:
```bash
pip install pytest
pytest tests/test_strategy.py -v --tb=long
```

## Adding New Tests

When adding new test scenarios:

1. **Use TestDataFactory** for consistent mock data creation
2. **Base on real patterns** observed in live data
3. **Test both positive and negative** cases
4. **Include edge cases** that might break the strategy
5. **Document expected behavior** clearly
6. **Use descriptive test names** that explain the scenario

## Continuous Testing

### Pre-deployment Checklist:
- [ ] All strategy tests pass
- [ ] Edge cases are covered
- [ ] Real data integration tests pass
- [ ] Performance tests within acceptable limits
- [ ] No regression in existing functionality

### Monitoring in Production:
- Log strategy decisions for analysis
- Monitor for unexpected None returns
- Track reallocation frequency and amounts
- Alert on constraint violations or errors

This comprehensive testing strategy ensures the bot behaves correctly under all conditions and provides confidence for production deployment.
