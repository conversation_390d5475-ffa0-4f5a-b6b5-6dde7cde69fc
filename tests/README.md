# Tests Directory

This directory contains all test scripts and utilities for the Morpho Reallocator Bot.

## Test Categories

### Core Functionality Tests
- **`simple_test.py`** - Basic functionality tests without complex dependencies
- **`test_current_status.py`** - Tests current implementation status and interface compliance
- **`test_data_module.py`** - Basic data module functionality tests
- **`test_real_api.py`** - Real API integration tests

### Comprehensive Tests
- **`test_comprehensive.py`** - Full test suite covering all components
- **`test_implementation.py`** - Implementation-specific tests
- **`test_interface_changes.py`** - Interface compliance and change detection tests

### Data Analysis & Debugging
- **`log_data_fetching.py`** - Comprehensive logging of all data fetching operations
- **`data_summary.py`** - Clean summary of current vault and market status
- **`web3_calls_demo.py`** - Demonstration of exact Web3 contract calls being made

### Strategy Tests
- **`test_strategy.py`** - Comprehensive strategy tests with realistic mock data
- **`TESTING_STRATEGY.md`** - Detailed testing strategy documentation

## Running Tests

### From Project Root
```bash
# Run individual tests
python tests/simple_test.py
python tests/test_current_status.py
python tests/data_summary.py

# Run strategy tests with comprehensive edge case coverage
python tests/test_strategy.py -v

# Run data analysis scripts
python tests/log_data_fetching.py
python tests/web3_calls_demo.py
```

### Environment Setup
Make sure to load environment variables before running tests:
```bash
source load_env.sh
python tests/simple_test.py
```

## Test Dependencies

All test scripts automatically handle path resolution to import from:
- Root directory (for `log.py`, `config/`)
- `src/` directory (for main modules)

The scripts use this pattern:
```python
# Add root directory and src to path
root_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, root_dir)
sys.path.insert(0, os.path.join(root_dir, 'src'))
```

## Test Types

### Unit Tests
- Test individual components in isolation
- Mock external dependencies when needed
- Fast execution

### Integration Tests
- Test real Web3 interactions
- Use actual vault and market data
- Require network connectivity

### Analysis Scripts
- Provide detailed logging and debugging information
- Help understand data flow and current system state
- Useful for troubleshooting and monitoring

## Adding New Tests

When adding new test scripts:

1. **Place in appropriate category** based on test type
2. **Use consistent naming** (`test_*.py` for test files)
3. **Include proper imports** using the path resolution pattern above
4. **Add documentation** explaining what the test covers
5. **Update this README** with the new test description

## Test Output

Tests use the centralized logging system (`log.py`) and provide:
- **Structured output** with clear success/failure indicators
- **Detailed error information** for debugging
- **Performance metrics** where applicable
- **Data summaries** for analysis scripts

## Troubleshooting

### Import Errors
If you see import errors, ensure:
- You're running from the project root directory
- Environment variables are loaded (`source load_env.sh`)
- All required dependencies are installed (`pip install -r requirements.txt`)

### Network Errors
For tests that use Web3:
- Check RPC endpoint configuration
- Verify network connectivity
- Ensure RPC provider is not rate-limiting

### Data Errors
For tests using real vault data:
- Verify vault address is correct
- Check that markets exist and are accessible
- Ensure contract ABIs are up to date
