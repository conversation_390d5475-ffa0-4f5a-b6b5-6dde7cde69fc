# Fuzz Testing for Morpho Reallocator Bot

## Overview

Fuzz testing (property-based testing) ensures the strategy is robust against **all possible input configurations**, not just the specific test cases we can think of. This is critical for financial applications where unexpected edge cases can cause significant issues.

## Why Fuzz Testing?

### **Traditional Testing Limitations**
- ✅ Tests specific scenarios we design
- ❌ Misses edge cases we don't think of
- ❌ Limited input combinations
- ❌ May not catch rare but critical failures

### **Fuzz Testing Benefits**
- ✅ Tests **thousands** of random input combinations
- ✅ Finds edge cases automatically
- ✅ Ensures **no crashes** under any valid input
- ✅ Verifies **invariants** (properties that should always be true)
- ✅ **Shrinks** failing inputs to minimal examples

## Framework: Hypothesis

We use **Hypothesis**, the leading property-based testing framework for Python:

```bash
pip install hypothesis
```

### **Key Features**
- **Automatic input generation** - Creates realistic but varied test data
- **Shrinking** - When a test fails, finds the simplest failing case
- **Stateful testing** - Can test sequences of operations
- **Reproducible** - Failed tests can be reproduced exactly

## Our Fuzz Tests

### **1. Crash Resistance (`test_strategy_never_crashes`)**
**Property**: Strategy should never crash regardless of input

**What it tests**:
- Random market configurations (1-12 markets)
- Various supply/borrow/vault amounts
- Different utilization rates
- Edge cases like very small/large numbers

**Why critical**: A crash in production could halt the bot

### **2. Fund Conservation (`test_fund_conservation_property`)**
**Property**: Total vault assets should always be conserved

**What it tests**:
- Reallocation preserves total funds across ALL markets
- No money is created or destroyed
- Correctly handles partial results (strategy only returns changed markets)
- Rounding errors are within acceptable limits

**Important**: Strategy only returns actions for markets that change. Unchanged markets must be accounted for separately.

**Why critical**: Fund loss would be catastrophic

### **3. Output Structure (`test_output_structure_property`)**
**Property**: When strategy returns a result, it should be well-formed

**What it tests**:
- Result is a list when not None
- Each action is properly structured
- Amounts are valid numbers (not NaN/Infinity)
- No more actions than markets

**Why critical**: Malformed output could cause execution errors

## Running Fuzz Tests

### **Quick Run**
```bash
python tests/test_fuzz.py
```

### **Integrated with Main Tests**
```bash
python tests/test_strategy.py  # Includes fuzz tests if Hypothesis installed
```

### **Custom Configuration**
```python
@settings(max_examples=1000, deadline=30000)  # More thorough testing
def test_strategy_never_crashes(self, market_configs):
    # ...
```

## Example Output

```
🧪 MORPHO REALLOCATOR BOT - FUZZ TESTING
============================================================
Testing strategy robustness with random inputs...
This may take a few minutes...
============================================================

test_fund_conservation_property ... ok
test_output_structure_property ... ok  
test_strategy_never_crashes ... ok

============================================================
✅ All fuzz tests passed! Strategy is robust.
   - No crashes with random inputs
   - Fund conservation maintained
   - Output structure always valid
============================================================
```

## What Fuzz Tests Catch

### **Real Examples of Issues Fuzz Testing Finds**:
1. **Division by zero** with empty markets
2. **Integer overflow** with very large amounts
3. **Precision loss** with very small amounts
4. **Invalid state** combinations
5. **Memory issues** with many markets
6. **Floating point edge cases** (NaN, Infinity)

### **In Our Context**:
- Markets with zero supply/borrow
- Extreme utilization rates (0%, 100%)
- Very large vault amounts
- Tiny fractional amounts
- All markets over/under target simultaneously
- Markets not in priority list

## Integration with CI/CD

Add to your CI pipeline:

```yaml
- name: Run Fuzz Tests
  run: |
    pip install hypothesis
    python tests/test_fuzz.py
```

## Best Practices

### **Property Design**
1. **Focus on invariants** - What should ALWAYS be true?
2. **Test boundaries** - Edge cases are where bugs hide
3. **Keep tests fast** - Use reasonable `max_examples`
4. **Use realistic data** - Generate data that resembles production

### **Our Strategy Properties**
- ✅ **Never crash** (most important)
- ✅ **Conserve funds** (critical for finance)
- ✅ **Valid output structure** (prevents execution errors)
- ✅ **Respect constraints** (caps, minimums)

## Extending Fuzz Tests

To add new properties:

```python
@given(st.lists(realistic_market_data(), min_size=1, max_size=12))
def test_new_property(self, market_configs):
    """Property: Describe what should always be true."""
    # Setup markets
    # Run strategy
    # Assert property holds
```

## Performance

- **50-100 examples**: Quick smoke test (~30 seconds)
- **500-1000 examples**: Thorough testing (~5 minutes)
- **5000+ examples**: Exhaustive testing (~30+ minutes)

Choose based on your needs and CI time constraints.

## Conclusion

Fuzz testing provides **confidence** that your strategy will handle **any** input gracefully. In financial applications, this level of robustness is essential for production deployment.

**Remember**: Traditional tests verify expected behavior, fuzz tests verify **unexpected behavior** doesn't break the system.
