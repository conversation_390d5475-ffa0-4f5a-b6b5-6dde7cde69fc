#!/usr/bin/env python3
"""
Test script for the data module implementation.

This script tests the GraphQL-based data collection from The Graph
and validates interface compliance.
"""

import os
import sys
from log import get_logger
from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Try to load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed, using system environment variables only")

# Setup logging
logger = get_logger(__name__)


def test_graphql_client():
    """Test GraphQL client connection."""
    logger.info("Testing GraphQL client connection...")
    
    try:
        from graph_client.client import create_client
        
        client = create_client(8453)  # Base chain
        logger.info(f"Client info: {client.get_chain_info()}")
        
        # Test connection
        if client.test_connection():
            logger.info("✅ GraphQL client connection successful")
            return True
        else:
            logger.error("❌ GraphQL client connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ GraphQL client test failed: {e}")
        return False


def test_market_data():
    """Test market data collection."""
    logger.info("Testing market data collection...")
    
    try:
        from market_data import MarketData
        
        test_market_id = "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89"
        
        market_data = MarketData(test_market_id, chain_id=8453)
        
        # Test connection first
        if not market_data.test_connection():
            logger.error("❌ Market data connection test failed")
            return False
        
        logger.info("✅ Market data connection successful")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ Market data test failed: {e}")
        return False


def test_vault_data():
    """Test vault data collection."""
    logger.info("Testing vault data collection...")
    
    try:
        from vault_data import VaultData
        
        # Use a placeholder vault address
        test_vault_address = "0x1D3b1Cd0a0f242d598834b3F2d126dC6bd774657"
        
        vault_data = VaultData(test_vault_address, chain_id=8453)
        
        if vault_data.data is not None:
            logger.info("✅ Vault data fetched successfully")
        else:
            logger.error("❌ Vault data is not populated")

        logger.info(PrioritizedUtilizationStrategy().find_reallocation(vault_data))

        return True
        
    except Exception as e:
        logger.error(f"❌ Vault data test failed: {e}")
        return False


def test_interface_compliance():
    """Test that our implementations comply with interfaces."""
    logger.info("Testing interface compliance...")

    try:
        # Import interfaces first
        from interfaces.market_interface import IMarketData
        from interfaces.vault_interface import IVaultData

        # Then import implementations
        import market_data
        import vault_data
        
        # Test that our classes implement the interfaces
        test_market_id = "test_market"
        test_vault_address = "test_vault"

        # Create instances
        market_data_instance = market_data.MarketData(test_market_id)
        vault_data_instance = vault_data.VaultData(test_vault_address)

        # Check that they are instances of the interfaces
        assert isinstance(market_data_instance, IMarketData), "MarketData must implement IMarketData"
        assert isinstance(vault_data_instance, IVaultData), "VaultData must implement IVaultData"

        # Check that required methods exist
        assert hasattr(market_data_instance, 'fetch_market_data'), "MarketData must have fetch_market_data method"
        assert hasattr(market_data_instance, 'fetch_market_params'), "MarketData must have fetch_market_params method"
        assert hasattr(market_data_instance, 'fetch_market_state'), "MarketData must have fetch_market_state method"
        assert hasattr(vault_data_instance, 'fetch_vault_data'), "VaultData must have fetch_vault_data method"
        
        logger.info("✅ Interface compliance test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Interface compliance test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    logger.info("Testing configuration...")

    try:
        # Import config components individually to avoid circular imports
        import config.config as cfg
        
        # Check API key
        if not cfg.THE_GRAPH_API_KEY:
            logger.error("❌ THE_GRAPH_API_KEY not configured")
            return False

        logger.info(f"✅ API key configured: {cfg.THE_GRAPH_API_KEY[:10]}...")

        # Check chain config
        base_config = cfg.get_chain_config(8453)
        if not base_config:
            logger.error("❌ Base chain config not found")
            return False

        logger.info(f"✅ Base chain config: {base_config['name']}")

        # Check query URL
        query_url = cfg.get_query_url(8453)
        if not query_url:
            logger.error("❌ Query URL not generated")
            return False

        logger.info(f"✅ Query URL generated: {query_url[:50]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("🚀 Starting data module tests...")
    
    tests = [
        # TODO: Uncomment to test other components
        # ("Configuration", test_configuration),
        # ("Interface Compliance", test_interface_compliance),
        # ("GraphQL Client", test_graphql_client),
        # ("Market Data", test_market_data),
        ("Vault Data", test_vault_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.error("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    main()
