#!/usr/bin/env python3
"""
<PERSON>ript to fetch real market data from the vault and generate hardcoded test data.
Run this once to get the real market parameters, then copy the output into the test file.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from vault_data import VaultData
from config import config

def fetch_and_format_market_data():
    """Fetch real market data and format it for hardcoding in tests."""
    try:
        print("🔄 Fetching real market data from vault...")
        vault_data = VaultData(config.VAULT_ADDRESS)
        markets = vault_data.data["market_data"]
        
        print(f"✅ Fetched data for {len(markets)} markets")
        print("\n" + "="*80)
        print("REAL MARKET DATA FOR HARDCODING IN TESTS")
        print("="*80)
        
        print("\n# Real market parameters (fetched from live vault)")
        print("REAL_MARKET_PARAMS = {")
        
        for i, market in enumerate(markets):
            market_id = market.data["id"]
            params = market.params
            
            print(f'    "{market_id}": {{')
            print(f'        "loan_token": "{params["loan_token"]}",')
            print(f'        "collateral_token": "{params["collateral_token"]}",')
            print(f'        "oracle": "{params["oracle"]}",')
            print(f'        "irm": "{params["irm"]}",')
            print(f'        "lltv": {params["lltv"]},')
            print(f'    }},')
        
        print("}")
        
        print("\n# Real market state examples (for reference)")
        print("REAL_MARKET_STATE_EXAMPLES = {")
        
        for i, market in enumerate(markets):
            market_id = market.data["id"]
            state = market.state
            data = market.data
            
            print(f'    "{market_id}": {{')
            print(f'        "total_supply_assets": {state["total_supply_assets"]},')
            print(f'        "total_borrow_assets": {state["total_borrow_assets"]},')
            print(f'        "vault_assets": {data["vault_assets"]},')
            print(f'        "cap": {data["cap"]},')
            print(f'    }},')
        
        print("}")
        
        print("\n" + "="*80)
        print("Copy the above dictionaries into your test file!")
        print("="*80)
        
    except Exception as e:
        print(f"❌ Error fetching market data: {e}")
        print("Make sure you have network access and the vault address is correct.")

if __name__ == "__main__":
    fetch_and_format_market_data()
