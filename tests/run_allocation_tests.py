#!/usr/bin/env python3
"""
Run key strategy tests with detailed allocation output for sanity checking.
"""

import subprocess
import sys

def run_test(test_name):
    """Run a specific test and capture output."""
    cmd = [sys.executable, "tests/test_strategy.py", f"TestPrioritizedUtilizationStrategy.{test_name}", "-v"]
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
    return result.stdout, result.stderr, result.returncode

def main():
    """Run key allocation tests with detailed output."""
    
    key_tests = [
        ("test_no_reallocation_needed_balanced_markets", "Balanced Markets (No Action)"),
        ("test_simple_reallocation_over_under_target", "Simple Over/Under Target"),
        ("test_empty_vault_no_assets", "Empty Vault - No Assets"),
        ("test_extreme_utilization_scenarios", "Extreme Utilization Scenarios"),
        ("test_priority_order_respected", "Priority Order Respected"),
        ("test_overall_high_utilization_priority_withdrawal", "Overall High Utilization"),
        ("test_overall_low_utilization_priority_deposit", "Overall Low Utilization"),
        ("test_mixed_utilization_priority_rebalancing", "Mixed Utilization Rebalancing"),
        ("test_priority_order_with_limited_funds", "Priority with Limited Funds"),
        ("test_priority_withdrawal_order", "Priority Withdrawal Order"),
        ("test_priority_order_realistic_scenario", "Realistic Mixed Scenario"),
        ("test_large_vault_many_markets", "Large Vault Many Markets"),
        ("test_markets_not_in_priority_list", "Markets Not in Priority List"),
        ("test_zero_supply_market", "Zero Supply Market Edge Case"),
        ("test_cap_constraints", "Supply Cap Constraints"),
        ("test_realistic_12_market_current_state", "🎯 REALISTIC: 12-Market Current State"),
        ("test_realistic_12_market_rebalance_needed", "🎯 REALISTIC: 12-Market Rebalance Scenario"),
    ]
    
    print("🧪 MORPHO REALLOCATOR BOT - COMPLETE ALLOCATION SANITY CHECK")
    print("=" * 80)
    print("Running ALL strategy tests to verify allocation decisions make sense...")
    print(f"Total tests: {len(key_tests)}")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, description in key_tests:
        print(f"\n🔍 Running: {description}")
        print("-" * 60)
        
        stdout, stderr, returncode = run_test(test_name)
        
        if returncode == 0:
            print(stdout)
            passed += 1
        else:
            print(f"❌ Test failed: {test_name}")
            print(f"Error: {stderr}")
            failed += 1
    
    print("\n" + "=" * 80)
    print(f"📊 SUMMARY: {passed} passed, {failed} failed")
    print("=" * 80)
    
    if failed == 0:
        print("✅ All allocation decisions look correct!")
        print("   - Priority order is respected")
        print("   - Fund conservation works")
        print("   - Strategy makes sensible decisions")

        # Offer to run fuzz tests
        print("\n" + "=" * 80)
        print("🎲 OPTIONAL: Run fuzz tests to verify robustness?")
        print("   This tests the strategy with thousands of random inputs")
        print("   Requires: pip install hypothesis")
        print("   Run: python tests/test_fuzz.py")
    else:
        print("❌ Some tests failed - check allocation logic")
    
    return failed

if __name__ == "__main__":
    sys.exit(main())
