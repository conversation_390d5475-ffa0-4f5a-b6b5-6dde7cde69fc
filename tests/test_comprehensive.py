#!/usr/bin/env python3
"""
Comprehensive test suite for the morpho-reallocator-bot data collection system.

This test suite covers all major functionality including:
- Market data collection and caching
- Vault data collection and caching
- GraphQL client functionality
- Data validation and transformation
- Error handling and edge cases
"""

import os
import sys
import time
from log import get_logger
import unittest
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Try to load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed, using system environment variables only")

# Setup logging
logger = get_logger(__name__)


class TestMarketDataCollection(unittest.TestCase):
    """Test market data collection functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        from market_data import MarketData
        # Use a real market ID from Base chain
        self.market_id = "0x10b0ce8c2db5e51338caa54effa56c4dd450527a31b12d60e8a33e8fdaa4ebb1"
        self.market_data = MarketData(self.market_id)
    
    def test_market_data_initialization(self):
        """Test market data object initialization."""
        self.assertEqual(self.market_data.market_id, self.market_id)
        self.assertEqual(self.market_data.chain_id, 8453)  # Base chain
        self.assertIsNotNone(self.market_data.collector)
    
    def test_fetch_market_params(self):
        """Test fetching market parameters."""
        params = self.market_data.fetch_market_params(self.market_id)
        
        # Validate structure
        self.assertIsInstance(params, dict)
        required_fields = ["loan_token", "collateral_token", "irm", "oracle", "lltv"]
        for field in required_fields:
            self.assertIn(field, params)
            self.assertIsInstance(params[field], str)
    
    def test_fetch_market_state(self):
        """Test fetching market state."""
        state = self.market_data.fetch_market_state(self.market_id)
        
        # Validate structure
        self.assertIsInstance(state, dict)
        required_fields = ["total_supply_assets", "total_supply_shares", 
                          "total_borrow_assets", "total_borrow_shares", 
                          "last_update", "fee"]
        for field in required_fields:
            self.assertIn(field, state)
            self.assertIsInstance(state[field], str)
    
    def test_fetch_complete_market_data(self):
        """Test fetching complete market data."""
        data = self.market_data.fetch_market_data(self.market_id)
        
        # Validate structure
        self.assertIsInstance(data, dict)
        required_fields = ["chain_id", "id", "params", "state", "cap", "vault_assets", "rate_at_target"]
        for field in required_fields:
            self.assertIn(field, data)
        
        # Validate nested structures
        self.assertIsInstance(data["params"], dict)
        self.assertIsInstance(data["state"], dict)
    
    def test_caching_functionality(self):
        """Test caching functionality."""
        # First fetch
        start_time = time.time()
        data1 = self.market_data.fetch_market_data(self.market_id)
        first_time = time.time() - start_time
        
        # Second fetch (should be cached)
        start_time = time.time()
        data2 = self.market_data.fetch_market_data(self.market_id)
        second_time = time.time() - start_time
        
        # Verify caching
        self.assertEqual(data1, data2)
        self.assertLess(second_time, first_time)
        self.assertTrue(self.market_data.is_cache_valid("data"))
    
    def test_cache_expiration(self):
        """Test cache expiration functionality."""
        # Fetch data to populate cache
        self.market_data.fetch_market_data(self.market_id)
        self.assertTrue(self.market_data.is_cache_valid("data"))
        
        # Clear cache
        self.market_data.clear_cache()
        self.assertFalse(self.market_data.is_cache_valid("data"))


class TestVaultDataCollection(unittest.TestCase):
    """Test vault data collection functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        from vault_data import VaultData
        # Use a real vault address from Base chain
        self.vault_address = "0x00dfdb8c7295a03dcf1adff4d21eb5d9d19fb330"
        self.vault_data = VaultData(self.vault_address)
    
    def test_vault_data_initialization(self):
        """Test vault data object initialization."""
        self.assertEqual(self.vault_data.vault_address, self.vault_address)
        self.assertEqual(self.vault_data.chain_id, 8453)  # Base chain
        self.assertIsNotNone(self.vault_data.collector)
    
    def test_get_vault_markets(self):
        """Test getting vault markets."""
        markets = self.vault_data.get_vault_markets()
        
        self.assertIsInstance(markets, list)
        self.assertGreater(len(markets), 0)
        for market_id in markets:
            self.assertIsInstance(market_id, str)
            self.assertTrue(market_id.startswith("0x"))
    
    def test_get_supply_queue(self):
        """Test getting vault supply queue."""
        supply_queue = self.vault_data.get_supply_queue()
        
        self.assertIsInstance(supply_queue, list)
        for market_id in supply_queue:
            self.assertIsInstance(market_id, str)
            self.assertTrue(market_id.startswith("0x"))
    
    def test_get_withdraw_queue(self):
        """Test getting vault withdraw queue."""
        withdraw_queue = self.vault_data.get_withdraw_queue()
        
        self.assertIsInstance(withdraw_queue, list)
        for market_id in withdraw_queue:
            self.assertIsInstance(market_id, str)
            self.assertTrue(market_id.startswith("0x"))
    
    def test_fetch_vault_data(self):
        """Test fetching complete vault data."""
        data = self.vault_data.fetch_vault_data(self.vault_address)
        
        # Validate structure
        self.assertIsInstance(data, dict)
        self.assertIn("vault_address", data)
        self.assertIn("market_data", data)
        self.assertEqual(data["vault_address"], self.vault_address)
        self.assertIsInstance(data["market_data"], list)
    
    def test_vault_caching(self):
        """Test vault data caching."""
        # First fetch
        start_time = time.time()
        data1 = self.vault_data.fetch_vault_data(self.vault_address)
        first_time = time.time() - start_time
        
        # Second fetch (should be cached)
        start_time = time.time()
        data2 = self.vault_data.fetch_vault_data(self.vault_address)
        second_time = time.time() - start_time
        
        # Verify caching
        self.assertEqual(len(data1["market_data"]), len(data2["market_data"]))
        self.assertLess(second_time, first_time)
        self.assertTrue(self.vault_data.is_cache_valid())


class TestGraphQLClient(unittest.TestCase):
    """Test GraphQL client functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        from graph_client.client import create_client
        self.client = create_client(8453)  # Base chain
    
    def test_client_initialization(self):
        """Test client initialization."""
        self.assertEqual(self.client.chain_id, 8453)
        self.assertIsNotNone(self.client.query_url)
        self.assertIsNotNone(self.client.chain_config)
    
    def test_connection(self):
        """Test client connection."""
        self.assertTrue(self.client.test_connection())
    
    def test_chain_info(self):
        """Test getting chain information."""
        info = self.client.get_chain_info()
        
        self.assertIsInstance(info, dict)
        self.assertEqual(info["chain_id"], 8453)
        self.assertEqual(info["chain_name"], "Base")
        self.assertIn("query_url", info)


class TestDataValidation(unittest.TestCase):
    """Test data validation functionality."""
    
    def test_market_data_validation(self):
        """Test market data validation."""
        from graph_client.transformers import validate_market_data
        
        # Valid market data
        valid_data = {
            "chain_id": "8453",
            "id": "0x123",
            "params": {
                "loan_token": "0x456",
                "collateral_token": "0x789",
                "irm": "0xabc",
                "oracle": "0xdef",
                "lltv": "800000000000000000"
            },
            "state": {
                "total_supply_assets": "1000000",
                "total_supply_shares": "1000000",
                "total_borrow_assets": "500000",
                "total_borrow_shares": "500000",
                "last_update": "1234567890",
                "fee": "100000000000000000"
            },
            "cap": "0",
            "vault_assets": "0",
            "rate_at_target": "0"
        }
        
        self.assertTrue(validate_market_data(valid_data))
        
        # Invalid market data (missing field)
        invalid_data = valid_data.copy()
        del invalid_data["params"]
        self.assertFalse(validate_market_data(invalid_data))
    
    def test_vault_data_validation(self):
        """Test vault data validation."""
        from graph_client.transformers import validate_vault_data
        
        # Valid vault data
        valid_data = {
            "vault_address": "0x123",
            "market_data": []
        }
        
        self.assertTrue(validate_vault_data(valid_data))
        
        # Invalid vault data (wrong type)
        invalid_data = {
            "vault_address": "0x123",
            "market_data": "not a list"
        }
        
        self.assertFalse(validate_vault_data(invalid_data))


if __name__ == "__main__":
    # Check if API key is configured
    api_key = os.getenv("THE_GRAPH_API_KEY")
    if not api_key:
        print("❌ THE_GRAPH_API_KEY not set. Please configure it before running tests.")
        sys.exit(1)
    
    print("🚀 Running comprehensive test suite...")
    unittest.main(verbosity=2)
