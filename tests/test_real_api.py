#!/usr/bin/env python3
"""
Test script for real API calls to The Graph.

This script tests our GraphQL implementation with actual API calls to the Base subgraph.
"""

import os
import sys
from log import get_logger

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logger = get_logger(__name__)


def test_real_graphql_connection():
    """Test real connection to The Graph Base subgraph."""
    logger.info("Testing real GraphQL connection to Base subgraph...")
    
    try:
        import requests
        import json
        
        # Get environment variables
        api_key = os.getenv("THE_GRAPH_API_KEY")
        subgraph_id = os.getenv("BASE_SUBGRAPH_ID", "71ZTy1veF9twER9CLMnPWeLQ7GZcwKsjmygejrgKirqs")
        
        if not api_key:
            logger.error("❌ THE_GRAPH_API_KEY not set")
            return False
        
        # Construct query URL
        query_url = f"https://gateway.thegraph.com/api/{api_key}/subgraphs/id/{subgraph_id}"
        logger.info(f"Query URL: {query_url}")
        
        # Simple test query to get markets (more reliable than protocols)
        test_query = """
        query TestConnection {
            markets(first: 1) {
                id
                name
                totalValueLockedUSD
            }
        }
        """
        
        payload = {
            "query": test_query,
            "variables": {}
        }
        
        # Make the request with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempt {attempt + 1}/{max_retries}")
                response = requests.post(
                    query_url,
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "User-Agent": "morpho-reallocator-bot/1.0"
                    },
                    timeout=15  # Shorter timeout for unstable connection
                )
                break  # Success, exit retry loop
            except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as e:
                logger.warning(f"⚠️ Network error on attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    raise  # Re-raise on final attempt
                import time
                time.sleep(2)  # Wait before retry
        
        logger.info(f"Response status: {response.status_code}")
        
        if response.status_code != 200:
            logger.error(f"❌ HTTP error: {response.status_code} - {response.text}")
            return False
        
        data = response.json()
        logger.info(f"Response data: {json.dumps(data, indent=2)}")
        
        # Check for GraphQL errors
        if "errors" in data:
            logger.error(f"❌ GraphQL errors: {data['errors']}")
            return False
        
        if "data" not in data:
            logger.error("❌ No data field in response")
            return False
        
        markets = data["data"].get("markets", [])
        if markets:
            market = markets[0]
            logger.info(f"✅ Connected successfully!")
            logger.info(f"✅ Sample market: {market.get('name', 'Unknown')}")
            logger.info(f"✅ Market TVL: ${market.get('totalValueLockedUSD', '0')}")
        else:
            logger.info("✅ Connection successful, but no markets found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Real GraphQL test failed: {e}")
        return False


def test_markets_query():
    """Test fetching markets from the subgraph."""
    logger.info("Testing markets query...")
    
    try:
        import requests
        import json
        
        api_key = os.getenv("THE_GRAPH_API_KEY")
        subgraph_id = os.getenv("BASE_SUBGRAPH_ID", "71ZTy1veF9twER9CLMnPWeLQ7GZcwKsjmygejrgKirqs")
        query_url = f"https://gateway.thegraph.com/api/{api_key}/subgraphs/id/{subgraph_id}"
        
        # Query to get some markets
        markets_query = """
        query GetMarkets {
            markets(first: 3, where: { totalSupply_gt: "0" }, orderBy: totalSupply, orderDirection: desc) {
                id
                name
                inputToken {
                    id
                    symbol
                }
                borrowedToken {
                    id
                    symbol
                }
                totalValueLockedUSD
                totalSupply
                totalBorrow
                isActive
            }
        }
        """
        
        payload = {
            "query": markets_query,
            "variables": {}
        }
        
        response = requests.post(query_url, json=payload, timeout=30)
        
        if response.status_code != 200:
            logger.error(f"❌ Markets query failed: {response.status_code}")
            return False
        
        data = response.json()
        
        if "errors" in data:
            logger.error(f"❌ Markets query errors: {data['errors']}")
            return False
        
        markets = data["data"].get("markets", [])
        logger.info(f"✅ Found {len(markets)} markets")
        
        for i, market in enumerate(markets):
            logger.info(f"Market {i+1}:")
            logger.info(f"  ID: {market.get('id', 'N/A')}")
            logger.info(f"  Name: {market.get('name', 'N/A')}")
            logger.info(f"  TVL: ${market.get('totalValueLockedUSD', '0')}")
            logger.info(f"  Active: {market.get('isActive', False)}")
        
        return len(markets) > 0
        
    except Exception as e:
        logger.error(f"❌ Markets query test failed: {e}")
        return False


def test_vaults_query():
    """Test fetching vaults from the subgraph."""
    logger.info("Testing vaults query...")
    
    try:
        import requests
        import json
        
        api_key = os.getenv("THE_GRAPH_API_KEY")
        subgraph_id = os.getenv("BASE_SUBGRAPH_ID", "71ZTy1veF9twER9CLMnPWeLQ7GZcwKsjmygejrgKirqs")
        query_url = f"https://gateway.thegraph.com/api/{api_key}/subgraphs/id/{subgraph_id}"
        
        # Query to get some vaults
        vaults_query = """
        query GetVaults {
            metaMorphos(first: 3) {
                id
                name
                symbol
                asset {
                    id
                    symbol
                }
                lastTotalAssets
                supplyQueue {
                    id
                    market {
                        id
                        name
                    }
                }
                withdrawQueue {
                    id
                    market {
                        id
                        name
                    }
                }
            }
        }
        """
        
        payload = {
            "query": vaults_query,
            "variables": {}
        }
        
        response = requests.post(query_url, json=payload, timeout=30)
        
        if response.status_code != 200:
            logger.error(f"❌ Vaults query failed: {response.status_code}")
            return False
        
        data = response.json()
        
        if "errors" in data:
            logger.error(f"❌ Vaults query errors: {data['errors']}")
            return False
        
        vaults = data["data"].get("metaMorphos", [])
        logger.info(f"✅ Found {len(vaults)} vaults")
        
        for i, vault in enumerate(vaults):
            logger.info(f"Vault {i+1}:")
            logger.info(f"  ID: {vault.get('id', 'N/A')}")
            logger.info(f"  Name: {vault.get('name', 'N/A')}")
            logger.info(f"  Symbol: {vault.get('symbol', 'N/A')}")
            logger.info(f"  Supply Queue Length: {len(vault.get('supplyQueue', []))}")
            logger.info(f"  Withdraw Queue Length: {len(vault.get('withdrawQueue', []))}")
        
        return len(vaults) > 0
        
    except Exception as e:
        logger.error(f"❌ Vaults query test failed: {e}")
        return False


def main():
    """Run real API tests."""
    logger.info("🚀 Starting real API tests...")
    
    # Check environment
    api_key = os.getenv("THE_GRAPH_API_KEY")
    if not api_key:
        logger.error("❌ THE_GRAPH_API_KEY not set. Please export it first.")
        return 1
    
    tests = [
        ("GraphQL Connection", test_real_graphql_connection),
        ("Markets Query", test_markets_query),
        ("Vaults Query", test_vaults_query),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("REAL API TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("🎉 All real API tests passed!")
        logger.info("Our GraphQL implementation is working with The Graph!")
        return 0
    else:
        logger.error("💥 Some real API tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
