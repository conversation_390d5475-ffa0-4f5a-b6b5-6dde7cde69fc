#!/usr/bin/env python3
"""
Data Fetching Logger Script

This script logs all the data being fetched from the Morpho vault and markets
to help understand the current data collection process.
"""

import sys
import os
from decimal import Decimal

# Add root directory and src to path
root_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, root_dir)
sys.path.insert(0, os.path.join(root_dir, 'src'))

from log import get_logger

logger = get_logger(__name__)

def log_vault_overview():
    """Log basic vault information."""
    from config import config
    
    logger.info("=" * 80)
    logger.info("🏦 VAULT OVERVIEW")
    logger.info("=" * 80)
    logger.info(f"Vault Address: {config.VAULT_ADDRESS}")
    logger.info(f"Chain ID: {config.PRIMARY_CHAIN_ID}")
    logger.info(f"RPC URL: {config.PRIMARY_RPC_URL}")
    logger.info(f"Morpho Address: {config.MORPHO_ADDRESS}")

def log_market_discovery():
    """Log the market discovery process."""
    from vault_data import VaultData
    from config import config
    
    logger.info("\n" + "=" * 80)
    logger.info("🔍 MARKET DISCOVERY")
    logger.info("=" * 80)
    
    vault_data = VaultData(config.VAULT_ADDRESS)
    
    logger.info(f"Fetching markets from vault withdraw queue...")
    markets = list(vault_data.get_vault_markets())
    logger.info(f"Found {len(markets)} markets in vault")
    
    for i, market_id in enumerate(markets, 1):
        logger.info(f"Market {i}: {market_id.hex()}")
    
    return markets

def log_market_details(market_id):
    """Log detailed information for a specific market."""
    from market_data import MarketData
    from config import config
    from utils.math import get_utilization
    
    logger.info(f"\n📊 MARKET DETAILS: {market_id.hex()}")
    logger.info("-" * 60)
    
    try:
        market_data = MarketData(market_id)
        
        # Log market parameters
        logger.info("🔧 Market Parameters:")
        params = market_data.params
        logger.info(f"  Loan Token: {params['loan_token']}")
        logger.info(f"  Collateral Token: {params['collateral_token']}")
        logger.info(f"  Oracle: {params['oracle']}")
        logger.info(f"  IRM: {params['irm']}")
        logger.info(f"  LLTV: {params['lltv']}")
        
        # Log market state
        logger.info("📈 Market State:")
        state = market_data.state
        total_supply = int(state['total_supply_assets'])
        total_borrow = int(state['total_borrow_assets'])
        utilization = get_utilization(state)
        
        logger.info(f"  Total Supply Assets: {total_supply:,} ({total_supply/1e18:.2f} tokens)")
        logger.info(f"  Total Supply Shares: {state['total_supply_shares']}")
        logger.info(f"  Total Borrow Assets: {total_borrow:,} ({total_borrow/1e18:.2f} tokens)")
        logger.info(f"  Total Borrow Shares: {state['total_borrow_shares']}")
        logger.info(f"  Last Update: {state['last_update']}")
        logger.info(f"  Fee: {state['fee']}")
        logger.info(f"  Utilization: {float(utilization)/1e18:.2%}")
        
        # Log vault-specific data
        logger.info("🏦 Vault Position:")
        vault_assets = market_data.data['vault_assets']
        cap = market_data.data['cap']
        logger.info(f"  Vault Assets: {vault_assets:,} ({vault_assets/1e18:.2f} tokens)")
        logger.info(f"  Supply Cap: {cap:,} ({cap/1e18:.2f} tokens)")
        
        # Log target and priority info
        target = config.target_util.get(market_id.hex())
        priority_index = None
        if market_id.hex() in config.priority_list:
            priority_index = config.priority_list.index(market_id.hex()) + 1
            
        logger.info("🎯 Strategy Configuration:")
        if target:
            logger.info(f"  Target Utilization: {float(target)/1e18:.2%}")
        else:
            logger.info("  Target Utilization: Not configured")
        
        if priority_index:
            logger.info(f"  Priority Rank: #{priority_index}")
        else:
            logger.info("  Priority Rank: Not in priority list")
            
        return market_data
        
    except Exception as e:
        logger.error(f"❌ Failed to fetch market data: {e}")
        return None

def log_strategy_analysis():
    """Log strategy analysis and reallocation decisions."""
    from vault_data import VaultData
    from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
    from config import config
    from utils.math import get_utilization, get_depositable_amount, get_withdrawable_amount
    
    logger.info("\n" + "=" * 80)
    logger.info("🧠 STRATEGY ANALYSIS")
    logger.info("=" * 80)
    
    vault_data = VaultData(config.VAULT_ADDRESS)
    strategy = PrioritizedUtilizationStrategy()
    
    logger.info("📋 Market Analysis Summary:")
    logger.info(f"{'Market':<12} {'Current':<8} {'Target':<8} {'Diff':<8} {'Assets':<12} {'Action'}")
    logger.info("-" * 70)
    
    total_withdrawable = 0
    total_depositable = 0
    
    for market in vault_data.data['market_data']:
        market_id = market.data['id']
        utilization = get_utilization(market.state)
        target = config.target_util.get(market_id, Decimal('0'))
        vault_assets = market.data['vault_assets']
        
        current_pct = float(utilization) / 1e18 * 100
        target_pct = float(target) / 1e18 * 100
        diff_pct = current_pct - target_pct
        
        action = "HOLD"
        if target > 0:
            if utilization < target:
                depositable = get_depositable_amount(market, target)
                total_depositable += depositable
                action = f"SUPPLY {depositable/1e18:.2f}"
            elif utilization > target:
                withdrawable = get_withdrawable_amount(market, target)
                total_withdrawable += withdrawable
                action = f"WITHDRAW {withdrawable/1e18:.2f}"
        
        logger.info(f"{market_id[:10]:<12} {current_pct:>6.1f}% {target_pct:>6.1f}% {diff_pct:>+6.1f}% {vault_assets/1e18:>10.2f} {action}")
    
    logger.info("-" * 70)
    logger.info(f"Total Withdrawable: {total_withdrawable/1e18:.2f} tokens")
    logger.info(f"Total Depositable: {total_depositable/1e18:.2f} tokens")
    logger.info(f"Reallocation Amount: {min(total_withdrawable, total_depositable)/1e18:.2f} tokens")
    
    # Test strategy
    logger.info("\n🔄 Strategy Execution:")
    reallocation = strategy.find_reallocation(vault_data)
    
    if reallocation:
        logger.info(f"✅ Strategy generated {len(reallocation)} reallocation actions:")
        for i, (params, new_amount) in enumerate(reallocation, 1):
            logger.info(f"  Action {i}: Set market to {new_amount/1e18:.2f} assets")
    else:
        logger.info("ℹ️ No reallocation needed - markets are within target ranges")

def log_web3_calls():
    """Log the actual Web3 calls being made."""
    from utils.web3_connector import Web3Connector
    from utils.web3_contract import Web3Contract
    from config import config
    
    logger.info("\n" + "=" * 80)
    logger.info("🌐 WEB3 CALLS DEMONSTRATION")
    logger.info("=" * 80)
    
    # Connect to Web3
    client = Web3Connector(provider_url=config.PRIMARY_RPC_URL).get_client()
    logger.info(f"Connected to Web3: {client.is_connected()}")
    logger.info(f"Latest block: {client.eth.block_number}")
    
    # Vault contract calls
    logger.info("\n🏦 Vault Contract Calls:")
    vault_contract = Web3Contract(client, config.VAULT_ADDRESS, config.VAULT_ABI)
    
    withdraw_queue_length = vault_contract.call("withdrawQueueLength")
    logger.info(f"withdrawQueueLength() -> {withdraw_queue_length}")
    
    if withdraw_queue_length > 0:
        first_market = vault_contract.call("withdrawQueue", [0])
        logger.info(f"withdrawQueue(0) -> {first_market.hex()}")
        
        market_config = vault_contract.call("config", [first_market])
        logger.info(f"config({first_market.hex()}) -> cap: {market_config[0]}, enabled: {market_config[1]}")
    
    # Morpho contract calls
    logger.info("\n🔷 Morpho Contract Calls:")
    morpho_contract = Web3Contract(client, config.MORPHO_ADDRESS, config.MORPHO_ABI)
    
    if withdraw_queue_length > 0:
        market_params = morpho_contract.call("idToMarketParams", [first_market])
        logger.info(f"idToMarketParams({first_market.hex()}) -> {market_params}")
        
        market_state = morpho_contract.call("market", [first_market])
        logger.info(f"market({first_market.hex()}) -> {market_state}")
        
        position = morpho_contract.call("position", [first_market, config.VAULT_ADDRESS])
        logger.info(f"position({first_market.hex()}, {config.VAULT_ADDRESS}) -> {position}")

def main():
    """Main logging function."""
    logger.info("🚀 Starting Data Fetching Analysis...")
    
    try:
        # Log vault overview
        log_vault_overview()
        
        # Log market discovery
        markets = log_market_discovery()
        
        # Log details for first few markets
        logger.info(f"\n📊 Detailed analysis for first 3 markets:")
        for i, market_id in enumerate(markets[:3]):
            log_market_details(market_id)
        
        # Log strategy analysis
        log_strategy_analysis()
        
        # Log Web3 calls
        log_web3_calls()
        
        logger.info("\n" + "=" * 80)
        logger.info("✅ DATA FETCHING ANALYSIS COMPLETE")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
