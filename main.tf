provider "aws" {
  region = "eu-central-1"
}

resource "aws_s3_bucket" "morpho_reallocator_bot_state" {
  bucket = "morpho-reallocator-bot-terraform-state"

  versioning {
    enabled = true
  }

  tags = {
    Name        = "Morpho Reallocator Bot State"
    Environment = "Production"
  }
}

terraform {
  backend "s3" {
    bucket  = "morpho-reallocator-bot-terraform-state"
    key     = "terraform.tfstate"
    region  = "eu-central-1"
    encrypt = true
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "encryption" {
  bucket = aws_s3_bucket.morpho_reallocator_bot_state.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_lambda_function" "morpho_reallocator_bot_handler" {
  function_name = "morpho-reallocator-bot-handler"
  package_type  = "Image"
  role          = "arn:aws:iam::236840575805:role/lambda_exec_role"
  image_uri     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-handler:latest"
  architectures = ["x86_64"]
  timeout       = 300
  memory_size   = 1024
  tags = {
    Name = "morpho-reallocator-bot-handler"
  }

  environment {
    variables = {
      cloud_env = "morpho-reallocator-bot-handler"
    }
  }
}

resource "aws_cloudwatch_log_group" "morpho_reallocator_bot_log_group" {
  name = "/aws/lambda/morpho-reallocator-bot-handler"
}

resource "aws_cloudwatch_event_rule" "schedule" {
  name        = "morpho-reallocator-bot-schedule"
  description = "Trigger Lambda every hour"
  schedule_expression = "rate(1 hour)"
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.schedule.name
  target_id = "morpho-reallocator-bot"
  arn       = aws_lambda_function.morpho_reallocator_bot_handler.arn
}

resource "aws_lambda_permission" "cloudwatch" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.morpho_reallocator_bot_handler.arn
  principal     = "events.amazonaws.com"
}

resource "aws_cloudwatch_log_metric_filter" "morpho_reallocator_bot_error_log_filter" {
  name           = "morpho-reallocator-bot-error-log-filter"
  pattern        = "\"error\""
  log_group_name = aws_cloudwatch_log_group.morpho_reallocator_bot_log_group.name
  metric_transformation {
    name      = "ErrorOccurrences"
    namespace = "MorphoReallocatorBot"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "morpho_reallocator_bot_error_alarm" {
  alarm_name                = "morpho-reallocator-bot-error-alarm"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = "1"
  metric_name               = "ErrorOccurrences"
  namespace                 = "MorphoReallocatorBot"
  period                    = 300
  statistic                 = "Sum"
  threshold                 = "1"
  alarm_description         = "This metric monitors log errors"
  actions_enabled           = true
  alarm_actions             = [aws_sns_topic.alerts.arn]
}

resource "aws_sns_topic" "alerts" {
  name = "morpho-reallocator-bot-alerts"
}

resource "aws_sns_topic_subscription" "gasan_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "colin_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "jashiel_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.alerts.arn
  policy = data.aws_iam_policy_document.sns_policy.json
}

data "aws_iam_policy_document" "sns_policy" {
  statement {
    actions = ["SNS:Publish"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    resources = [aws_sns_topic.alerts.arn]
  }
}

resource "aws_ecr_repository" "morpho_reallocator_bot_handler" {
  name = "morpho-reallocator-bot-handler"

  image_scanning_configuration {
    scan_on_push = true
  }

  encryption_configuration {
    encryption_type = "AES256"
  }

  tags = {
    Name        = "Morpho Reallocator Bot Handler"
    Environment = "Production"
  }
}
