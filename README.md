# Morpho Reallocator Bot

## Overview
The Morpho Reallocator Bot is an automated implementation designed to reallocate funds across markets in Morpho vaults based on target utilization ratios and priority strategies. It monitors market utilization and automatically rebalances allocations to maintain optimal capital efficiency.

The system is built with a focus on:
- **Direct Blockchain Data Access**: Real-time market and vault data fetching via Web3 contract calls
- **Priority-Based Strategy**: Intelligent reallocation based on market priority and target utilization
- **Automated Execution**: Integration with Fordefi for secure transaction execution
- **Interface Compliance**: Strict adherence to defined interfaces for consistency
- **Error Handling**: Comprehensive error handling and retry logic throughout the pipeline

## Project Structure
```
morpho-reallocator-bot
├── src/
│   ├── bot.py                    # Main bot logic and reallocation execution
│   ├── market_data.py            # MarketData class using Web3 contract calls
│   ├── vault_data.py             # VaultData class for vault operations
│   ├── interfaces/
│   │   ├── market_interface.py   # Interface definitions for market data
│   │   └── vault_interface.py    # Interface definitions for vault data
│   └── graph_client/             # Legacy GraphQL client (not actively used)
│       ├── client.py             # GraphQL client implementation
│       ├── market_collector.py   # Market data collection via GraphQL
│       ├── vault_collector.py    # Vault data collection via GraphQL
│       ├── queries.py            # GraphQL queries for Morpho Blue
│       ├── transformers.py       # Data transformation utilities
│       └── types.py              # Type definitions for GraphQL responses
├── strategies/
│   ├── strategy.py               # Base class for reallocation strategies
│   ├── equalize_util/
│   │   └── equalize_util.py      # Strategy for equalizing market utilization
│   └── priority_util/
│       └── priority_util.py      # Priority-based utilization strategy (active)
├── utils/
│   ├── math.py                   # Morpho-specific mathematical utilities
│   ├── web3_connector.py         # Web3 provider connections
│   ├── web3_contract.py          # Web3 contract interaction utilities
│   ├── create_transaction.py     # Transaction creation utilities
│   └── fordefi_transaction_helpers.py  # Fordefi integration for secure execution
├── config/
│   └── config.py                 # Centralized configuration (strategies, chains, targets)
├── tests/
│   ├── simple_test.py            # Basic functionality tests
│   ├── test_current_status.py    # Implementation status verification
│   ├── test_comprehensive.py     # Full test suite
│   ├── log_data_fetching.py      # Comprehensive data fetching analysis
│   ├── data_summary.py           # Clean vault and market status summary
│   ├── web3_calls_demo.py        # Web3 contract calls demonstration
│   └── README.md                 # Test documentation
├── README.md                     # This documentation
├── requirements.txt              # Python dependencies
├── load_env.sh                   # Environment variable loading script
├── Dockerfile                    # Docker containerization
└── main.tf                       # Terraform infrastructure configuration
```

## Installation

### Prerequisites
- Python 3.8 or higher
- Web3 RPC endpoint for Base network
- Fordefi vault access (for transaction execution)

### Setup
1. Clone the repository:
```bash
git clone <repository-url>
cd morpho-reallocator-bot
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment variables:
```bash
# Create .env file with required configuration
cp .env.example .env
# Edit .env file with your configuration
```

Required environment variables:
- `RPC_URL_8453`: RPC endpoint for Base network
- `WALLET_ADDRESS`: Wallet address for transaction execution
- `FORDEFI_VAULT_ID`: Fordefi vault ID for secure transaction signing
- `BASE_CHAIN_ID`: Chain ID for Base network (default: 8453)

Optional environment variables (for legacy GraphQL support):
- `THE_GRAPH_API_KEY`: Your The Graph API key (if using GraphQL fallback)
- `BASE_SUBGRAPH_ID`: Subgraph ID for Morpho Blue on Base

4. Load environment variables:
```bash
source load_env.sh
```

## Quick Start

### Running the Bot
```bash
# Load environment
source load_env.sh

# Run the reallocation bot
python -c "
from src.bot import ReallocationBot
bot = ReallocationBot()
bot.execute_reallocation()
"
```

### Manual Testing
```bash
# Check current market status
python -c "
from src.vault_data import VaultData
from config import config
from utils.math import get_utilization

vault_data = VaultData(config.VAULT_ADDRESS)
for market in vault_data.data['market_data']:
    util = get_utilization(market.state)
    target = config.target_util.get(market.data['id'], 0)
    print(f'Market {market.data[\"id\"][:10]}...: {float(util)/1e18:.1%} (target: {float(target)/1e18:.1%})')
"
```

## How It Works

### Data Collection System

The bot uses **direct Web3 contract calls** to fetch real-time data from the Morpho protocol and vault contracts on Base network. This approach provides the most up-to-date and accurate data compared to subgraph indexing.

#### Market Data Collection
The `MarketData` class fetches market information directly from Morpho contracts:
- **Market Parameters**: Loan token, collateral token, oracle, IRM, and LLTV from `idToMarketParams`
- **Market State**: Supply/borrow assets, shares, fees, and last update from `market` function
- **Vault Position**: Vault's supply shares and calculated assets from `position` function
- **Market Caps**: Supply caps configured in the vault from `config` function

```python
from src.market_data import MarketData

# Initialize with a market ID (bytes32 format)
market_data = MarketData(market_id=market_id_bytes, chain_id=8453)

# Access market data
print(f"Market utilization: {market_data.state['total_borrow_assets']} / {market_data.state['total_supply_assets']}")
print(f"Vault assets in market: {market_data.data['vault_assets']}")
```

#### Vault Data Collection
The `VaultData` class discovers and collects data for all markets in a vault:
- **Market Discovery**: Reads withdraw queue to find all vault markets
- **Batch Processing**: Fetches data for all markets simultaneously
- **Market Enhancement**: Combines market data with vault-specific information

```python
from src.vault_data import VaultData

# Initialize with vault address
vault_data = VaultData(vault_address="0x1D3b1Cd0a0f242d598834b3F2d126dC6bd774657")

# Access all market data
for market in vault_data.data['market_data']:
    print(f"Market {market.data['id']}: {market.data['vault_assets']} assets")
```

### Reallocation Strategy

The bot uses a **Priority-Based Utilization Strategy** that:

1. **Evaluates Current State**: Calculates utilization for each market and compares to target
2. **Identifies Imbalances**: Finds markets above/below target utilization
3. **Calculates Reallocation**: Determines optimal fund movements to reach targets
4. **Prioritizes Markets**: Uses configured priority list to determine reallocation order
5. **Executes Transactions**: Builds and submits reallocation transactions via Fordefi

#### Target Utilization Configuration
Each market has a configured target utilization ratio:

```python
# Example targets from config.py
target_util = {
    "0x669b68ae...": Decimal("0.91") * WAD,   # PT-USR Sept: 91%
    "0x2c10e050...": Decimal("0.91") * WAD,   # PT-cUSDO Jul: 91%
    "0x1478d70d...": Decimal("0.90") * WAD,   # RLP: 90%
    "0x99f294c4...": Decimal("0.90") * WAD,   # cUSDO: 90%
    # ... more markets
}
```

#### Priority-Based Reallocation
Markets are processed in priority order to ensure optimal capital allocation:

```python
from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy

strategy = PrioritizedUtilizationStrategy()
reallocation = strategy.find_reallocation(vault_data)

if reallocation:
    # Execute reallocation via bot
    bot = ReallocationBot()
    bot.execute_reallocation()
```

## Testing

### Running Tests
```bash
# Load environment variables
source load_env.sh

# Run basic functionality tests
python tests/simple_test.py

# Test current implementation status
python tests/test_current_status.py

# Run comprehensive strategy tests with edge cases
python tests/test_strategy.py -v

# Run comprehensive data analysis
python tests/log_data_fetching.py

# Get clean vault status summary
python tests/data_summary.py

# See exact Web3 calls being made
python tests/web3_calls_demo.py

# Test with real vault data
python -c "
from src.vault_data import VaultData
from config import config
vault_data = VaultData(config.VAULT_ADDRESS)
print(f'Loaded {len(vault_data.data[\"market_data\"])} markets')
"
```

### Test Coverage
- **Basic Functionality**: Core imports and interface compliance (`tests/simple_test.py`)
- **Web3 Integration**: Contract calls and data fetching (`tests/web3_calls_demo.py`)
- **Strategy Testing**: Comprehensive edge case testing with realistic mock data (`tests/test_strategy.py`)
- **Environment Setup**: Configuration and dependency validation (`tests/test_current_status.py`)
- **Data Analysis**: Comprehensive logging and debugging (`tests/log_data_fetching.py`, `tests/data_summary.py`)
- **Real Data Tests**: Integration with live Morpho contracts (all test files)

## Architecture

### Web3-Based Data Architecture
The system uses direct blockchain interaction for real-time data:

1. **Web3 Layer** (`utils/web3_connector.py`): Manages RPC connections with retry logic
2. **Contract Layer** (`utils/web3_contract.py`): Handles contract interactions and function calls
3. **Data Layer** (`src/market_data.py`, `src/vault_data.py`): Implements data collection logic
4. **Interface Layer** (`interfaces/*.py`): Defines strict contracts for data structures
5. **Strategy Layer** (`strategies/`): Implements reallocation algorithms
6. **Execution Layer** (`utils/fordefi_transaction_helpers.py`): Handles secure transaction execution

### Data Flow
1. **Market Discovery**: Vault contract's `withdrawQueue` provides list of markets
2. **Market Data Fetching**: For each market, fetch params, state, and vault position
3. **Utilization Calculation**: Calculate current utilization vs target for each market
4. **Strategy Execution**: Priority-based algorithm determines optimal reallocation
5. **Transaction Building**: Construct `reallocate` function call with new allocations
6. **Secure Execution**: Submit transaction via Fordefi for multi-sig approval

### Error Handling
- **Contract Call Retries**: Automatic retry for failed Web3 calls with exponential backoff
- **Validation**: Type checking and data validation at each layer
- **Graceful Degradation**: Continue operation even if some markets fail to load
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Transaction Safety**: Fordefi integration ensures secure transaction execution

## Configuration

### Environment Variables
```bash
# Required for Web3 data fetching
RPC_URL_8453=https://mainnet.base.org
WALLET_ADDRESS=0x...
FORDEFI_VAULT_ID=your_fordefi_vault_id

# Optional (with defaults)
BASE_CHAIN_ID=8453

# Legacy GraphQL support (optional)
THE_GRAPH_API_KEY=your_api_key_here
BASE_SUBGRAPH_ID=71ZTy1veF9twER9CLMnPWeLQ7GZcwKsjmygejrgKirqs
```

### Strategy Configuration
The bot's behavior is configured in `config/config.py`:

```python
# Target utilization for each market (as Decimal * WAD)
target_util = {
    "0x669b68ae...": Decimal("0.91") * WAD,   # PT-USR Sept: 91%
    "0x2c10e050...": Decimal("0.91") * WAD,   # PT-cUSDO Jul: 91%
    # ... more markets
}

# Priority order for reallocation
priority_list = [
    "0x669b68ae...",   # PT-USR Sept (highest priority)
    "0x2c10e050...",   # PT-cUSDO Jul
    # ... more markets in priority order
]
```

### Supported Networks
- **Base (8453)**: Primary implementation with full support
- **Ethereum (1)**: Configuration ready for future implementation
- **Arbitrum (42161)**: Configuration ready for future implementation

## Performance & Reliability

### Current Implementation Benefits
1. **Real-time Data**: Direct contract calls provide the most current state
2. **No Indexing Delays**: Eliminates subgraph synchronization lag
3. **Atomic Operations**: All market data fetched in single block context
4. **Reduced Dependencies**: No reliance on external indexing services
5. **Better Error Handling**: Direct control over retry logic and error recovery

### Performance Characteristics
- **Market Data Fetch**: ~1-2 seconds for 12 markets via Web3
- **Strategy Calculation**: Near-instantaneous utilization analysis
- **Transaction Building**: Efficient reallocation parameter construction
- **Memory Usage**: Minimal footprint with on-demand data fetching

### Reliability Features
- **Automatic Retries**: Exponential backoff for failed Web3 calls
- **Graceful Degradation**: Continue with partial data if some markets fail
- **Transaction Safety**: Fordefi integration for secure multi-sig execution
- **Comprehensive Logging**: Full audit trail of all operations

## Development Guidelines

### Code Style
- **Type Hints**: Comprehensive type annotations throughout
- **Interface Compliance**: Strict adherence to defined interfaces
- **Error Handling**: Comprehensive error handling with logging
- **Documentation**: Detailed docstrings for all public methods

### Adding New Features
1. **Define Interface**: Start with interface definition in `interfaces/`
2. **Implement Logic**: Add implementation in appropriate collector
3. **Add Validation**: Include validation in `transformers.py`
4. **Write Tests**: Add comprehensive tests in test files
5. **Update Documentation**: Update README and docstrings

## Troubleshooting

### Common Issues

#### RPC Connection Issues
```bash
# Error: Could not connect to RPC endpoint
export RPC_URL_8453=https://mainnet.base.org
source load_env.sh
```

#### Contract Call Failures
- Verify RPC endpoint is responsive and not rate-limited
- Check that contract addresses in config.py are correct
- Ensure wallet has sufficient balance for gas estimation

#### Market ID Format Issues
- Market IDs must be in bytes32 format (not string)
- Use proper hex encoding for market identifiers
- Verify market exists in the vault's withdraw queue

#### Strategy Execution Issues
- Check that target utilization values are properly configured
- Verify priority list contains valid market IDs
- Ensure Fordefi vault ID and credentials are correct

### Debugging
Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Evolution: From GraphQL to Web3

### Why the Change?
The project initially used The Graph's GraphQL API for data collection but evolved to direct Web3 contract calls for several reasons:

1. **Data Completeness**: Some critical data was missing or delayed in subgraph indexing
2. **Real-time Requirements**: Reallocation decisions need the most current market state
3. **Reliability**: Direct contract calls eliminate dependency on external indexing services
4. **Precision**: Avoid potential rounding errors or data transformation issues in subgraphs

### Legacy GraphQL Code
The original GraphQL implementation is preserved in the `src/graph_client/` directory:
- `client.py`: GraphQL client with retry logic
- `market_collector.py`: Market data collection via subgraph
- `vault_collector.py`: Vault data collection via subgraph
- `queries.py`: Optimized GraphQL queries
- `transformers.py`: Data transformation utilities

This code can be reactivated if needed by switching the imports in `market_data.py` and `vault_data.py`.

### Current Implementation Benefits
- **Accuracy**: Direct contract state access
- **Speed**: No indexing delays
- **Control**: Full control over retry logic and error handling
- **Simplicity**: Fewer moving parts and dependencies

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Install development dependencies
4. Run tests to ensure everything works
5. Make your changes
6. Add tests for new functionality
7. Update documentation
8. Submit a pull request

### Testing Requirements
- All new features must include tests
- Tests must pass on all supported Python versions
- Integration tests should use real Web3 calls when possible
- Strategy tests should verify reallocation logic with real market data
- Transaction tests should validate Fordefi integration (in test environment)
