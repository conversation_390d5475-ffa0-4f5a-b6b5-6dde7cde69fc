FORDEFI_BEARER_TOKEN='<bearer_token>'
FORDEFI_PRIVATE_KEY='<private_key>'

CHAIN_ID=8453
WALLET_ADDRESS='<wallet_address>'
FORDEFI_VAULT_ID='<fordefi_vault_id>'
THE_GRAPH_API_KEY='<your_api_key>'

# Base Chain Configuration (Primary Target)
BASE_CHAIN_ID=8453
BASE_SUBGRAPH_ID='71ZTy1veF9twER9CLMnPWeLQ7GZcwKsjmygejrgKirqs'
BASE_QUERY_URL='https://gateway.thegraph.com/api/<your_api_key>/subgraphs/id/71ZTy1veF9twER9CLMnPWeLQ7GZcwKsjmygejrgKirqs'

# Ethereum Mainnet Configuration (Future Support)
ETHEREUM_CHAIN_ID=1
ETHEREUM_SUBGRAPH_ID='8Lz789DP5VKLXumTMTgygjU2xtuzx8AhbaacgN5PYCAs'
ETHEREUM_QUERY_URL='https://gateway.thegraph.com/api/<your_api_key>/subgraphs/id/8Lz789DP5VKLXumTMTgygjU2xtuzx8AhbaacgN5PYCAs'

# Arbitrum Configuration (Future Support)
ARBITRUM_CHAIN_ID=42161
ARBITRUM_SUBGRAPH_ID='XsJn88DNCHJ1kgTqYeTgHMQSK4LuG1LR75339QVeQ26'
ARBITRUM_QUERY_URL='https://gateway.thegraph.com/api/<your_api_key>/subgraphs/id/XsJn88DNCHJ1kgTqYeTgHMQSK4LuG1LR75339QVeQ26'

# GraphQL Client Configuration
GRAPHQL_TIMEOUT=30
GRAPHQL_MAX_RETRIES=3
GRAPHQL_RETRY_DELAY=1

# Logging Configuration
LOG_LEVEL='INFO'

# Legacy RPC Configuration (for reference)
RPC_URL_1='https://eth-mainnet.g.alchemy.com/v2/<your_rpc_api_key>'
REALLOCATOR_PRIVATE_KEY_1='0x1234567890123456789012345678901234567890123456789012345678901234'
VAULT_WHITELIST_1='******************************************,******************************************'
EXECUTION_INTERVAL_1=900

RPC_URL_8453='https://base-mainnet.g.alchemy.com/v2/<your_rpc_api_key>'
REALLOCATOR_PRIVATE_KEY_8453='0x1234567890123456789012345678901234567890123456789012345678901234'
VAULT_WHITELIST_8453='******************************************'
EXECUTION_INTERVAL_8453=300