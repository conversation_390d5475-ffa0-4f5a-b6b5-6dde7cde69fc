from log import get_logger
import time
from typing import Dict, Any, Optional, List

from interfaces.vault_interface import IVaultData
from graph_client.vault_collector import create_vault_collector, VaultDataCollector
from config.config import PRIMARY_CHAIN_ID
from market_data import MarketData

logger = get_logger(__name__)

# Cache expiration time in seconds (5 minutes)
CACHE_EXPIRATION_SECONDS = 300


class VaultDataSubgraph(IVaultData):
    """
    Vault data implementation using The Graph GraphQL endpoint.

    This class implements the IVaultData interface and provides vault data
    collection from Morpho Blue subgraphs via The Graph.
    """

    def __init__(self, vault_address: str, chain_id: int = PRIMARY_CHAIN_ID):
        """
        Initialize VaultData instance.

        Args:
            vault_address: The vault address to fetch data for
            chain_id: Chain ID to fetch data from (default: Base)
        """
        self.vault_address = vault_address
        self.chain_id = chain_id
        self.collector: VaultDataCollector = create_vault_collector(chain_id)

        # Cache for fetched data with timestamp
        self._data_cache: Optional[Dict[str, Any]] = None
        self._data_cache_time: float = 0

        logger.info(f"Initialized VaultData for vault {vault_address} on chain {chain_id}")

    def fetch_vault_data(self, vault_address: str) -> dict:
        """
        Fetch complete vault data using The Graph GraphQL endpoint.

        This method implements the IVaultData.fetch_vault_data interface.
        Returns data in the exact format specified by the interface.

        Args:
            vault_address: The vault address to fetch data for

        Returns:
            Vault data in interface format:
            {
                "vault_address": "",
                "market_data": [
                    MarketData(
                        data={
                            "chain_id": "",
                            "id": "",
                            "params": {
                                "loan_token": "",
                                "collateral_token": "",
                                "irm": "",
                                "oracle": "",
                                "lltv": "",
                            },
                            "state": {
                                "total_supply_assets": "",
                                "total_supply_shares": "",
                                "total_borrow_assets": "",
                                "total_borrow_shares": "",
                                "last_update": "",
                                "fee": "",
                            },
                            "cap": "",
                            "vault_assets": "",
                            "rate_at_target": "",
                        },
                    )
                ],
            }
        """
        try:
            # Use cache if available, vault_address matches, and cache is not expired
            current_time = time.time()
            if (self._data_cache and
                self.vault_address == vault_address and
                current_time - self._data_cache_time < CACHE_EXPIRATION_SECONDS):
                logger.debug(f"Returning cached vault data for {vault_address}")
                return self._data_cache

            # Fetch fresh data from collector (returns dict format)
            logger.info(f"Fetching vault data for {vault_address}")
            raw_vault_data = self.collector.fetch_vault_data(vault_address)

            # Convert market data dictionaries to MarketData objects
            market_data_objects = []
            for market_dict in raw_vault_data.get("market_data", []):
                # Create MarketData object for each market
                market_obj = MarketData(market_dict["id"], self.chain_id)

                # Pre-populate the object's cache with the fetched data
                market_obj._data_cache = market_dict
                market_obj._params_cache = market_dict.get("params", {})
                market_obj._state_cache = market_dict.get("state", {})

                market_data_objects.append(market_obj)

            # Construct vault data with MarketData objects
            vault_data = {
                "vault_address": raw_vault_data.get("vault_address", vault_address),
                "market_data": market_data_objects
            }

            # Update cache with timestamp
            if self.vault_address == vault_address:
                self._data_cache = vault_data
                self._data_cache_time = current_time

            return vault_data

        except Exception as e:
            logger.error(f"Failed to fetch vault data for {vault_address}: {e}")
            raise

    def get_vault_markets(self, vault_address: str = None) -> List[str]:
        """
        Get list of market IDs associated with the vault.

        Args:
            vault_address: Vault address (uses instance address if None)

        Returns:
            List of market IDs
        """
        address = vault_address or self.vault_address
        return self.collector.get_vault_markets(address)

    def get_supply_queue(self, vault_address: str = None) -> List[str]:
        """
        Get vault's supply queue (ordered list of markets for deposits).

        Args:
            vault_address: Vault address (uses instance address if None)

        Returns:
            List of market IDs in supply queue order
        """
        address = vault_address or self.vault_address
        return self.collector.get_vault_supply_queue(address)

    def get_withdraw_queue(self, vault_address: str = None) -> List[str]:
        """
        Get vault's withdraw queue (ordered list of markets for withdrawals).

        Args:
            vault_address: Vault address (uses instance address if None)

        Returns:
            List of market IDs in withdraw queue order
        """
        address = vault_address or self.vault_address
        return self.collector.get_vault_withdraw_queue(address)

    def clear_cache(self) -> None:
        """Clear cached data to force fresh fetches."""
        self._data_cache = None
        self._data_cache_time = 0
        logger.debug(f"Cleared cache for vault {self.vault_address}")

    def is_cache_valid(self) -> bool:
        """
        Check if the vault data cache is valid (not expired).

        Returns:
            True if cache is valid, False otherwise
        """
        current_time = time.time()
        return (self._data_cache is not None and
               current_time - self._data_cache_time < CACHE_EXPIRATION_SECONDS)

    def test_connection(self) -> bool:
        """
        Test connection to The Graph endpoint.

        Returns:
            True if connection is successful
        """
        return self.collector.test_connection()

    def to_human_readable(self) -> str:
        """
        Convert the vault data to a human-readable string format.

        Returns:
            A string representation of the vault data.
        """
        if not self._data_cache:
            return "No data available. Fetch data first."

        vault_address = self._data_cache.get("vault_address", "Unknown")
        market_data = self._data_cache.get("market_data", [])

        result = [f"Vault Address: {vault_address}"]
        result.append("Market Data:")

        for market in market_data:
            result.append(f"  Market ID: {market.market_id}")
            result.append(f"    Chain ID: {market.chain_id}")
            result.append(f"    Cap: {market._data_cache.get('cap', 'Unknown')}")
            result.append(f"    Vault Assets: {market._data_cache.get('vault_assets', 'Unknown')}")
            result.append(f"    Rate at Target: {market._data_cache.get('rate_at_target', 'Unknown')}")

            params = market._params_cache
            result.append("    Params:")
            for key, value in params.items():
                result.append(f"      {key}: {value}")

            state = market._state_cache
            result.append("    State:")
            for key, value in state.items():
                result.append(f"      {key}: {value}")

        return "\n".join(result)
