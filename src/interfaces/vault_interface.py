# Interface for Vault Data Fetching (multiple implementations possible)

class IVaultData:
    def fetch_vault_data(self, vault_address: str) -> dict:
        '''
        Returns:
        "vault_address": "",
        "market_data": [
            MarketData(
                data={
                    "chain_id": "",
                    "id": "",
                    "params": {
                        "loan_token": "",
                        "collateral_token": "",
                        "irm": "",
                        "oracle": "",
                        "lltv": "",
                    },
                    "state": {
                        "total_supply_assets": "",
                        "total_supply_shares": "",
                        "total_borrow_assets": "",
                        "total_borrow_shares": "",
                        "last_update": "",
                        "fee": "",
                    },
                    "cap": "",
                    "vault_assets": "",
                    "rate_at_target": "",
                },
                params={
                    "loan_token": "",
                    "collateral_token": "",
                    "irm": "",
                    "oracle": "",
                    "lltv": "",
                },
                state={
                    "total_supply_assets": "",
                    "total_supply_shares": "",
                    "total_borrow_assets": "",
                    "total_borrow_shares": "",
                    "last_update": "",
                    "fee": "",
                },
            )
        ],
        '''
        pass
