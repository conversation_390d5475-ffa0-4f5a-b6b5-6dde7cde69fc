# Interface for Market Data Fetching (multiple implementations possible)

class IMarketData:
    def fetch_market_data(self, market_id: str) -> dict:
        '''
        Returns:
        {
            "chain_id": "",
            "id": "",
            "params": {
                "loan_token": "",
                "collateral_token": "",
                "irm": "",
                "oracle": "",
                "lltv": "",
            },
            "state": {
                "total_supply_assets": "",
                "total_supply_shares": "",
                "total_borrow_assets": "",
                "total_borrow_shares": "",
                "last_update": "",
                "fee": "",
            },
            "cap": "",
            "vault_assets": "",
            "rate_at_target": "",
        }
        '''
        pass

    def fetch_market_params(self, market_id: str) -> dict:
        '''
        Returns:
        {
            "loan_token": "",
            "collateral_token": "",
            "irm": "",
            "oracle": "",
            "lltv": "",
        }
        '''
        pass

    def fetch_market_state(self, market_id: str) -> dict:
        '''
        Returns:
        {
            "total_supply_assets": "",
            "total_supply_shares": "",
            "total_borrow_assets": "",
            "total_borrow_shares": "",
            "last_update": "",
            "fee": "",
        }
        '''
        pass