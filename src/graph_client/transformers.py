"""
Data transformers to convert GraphQL responses to interface-compliant formats.

This module handles the transformation of GraphQL response data to match the exact
format expected by the interfaces defined in src/interfaces/.

CRITICAL: These transformers must maintain strict compliance with interface contracts.
"""

from log import get_logger
from typing import Dict, Any, List, Optional
from decimal import Decimal

logger = get_logger(__name__)


class DataTransformationError(Exception):
    """Exception raised when data transformation fails."""
    pass


def transform_market_data(graphql_market: Dict[str, Any], chain_id: int, vault_assets: str = "0", cap: str = "0", rate_at_target: str = "0") -> Dict[str, Any]:
    """
    Transform GraphQL market data to interface format.
    
    Args:
        graphql_market: Market data from GraphQL response
        chain_id: Chain ID for the market
        vault_assets: Vault's assets in this market (default: "0")
        cap: Market cap (default: "0")
        rate_at_target: Current interest rate (default: "0")
        
    Returns:
        Market data in interface format
        
    Raises:
        DataTransformationError: If transformation fails
    """
    try:
        # Extract market parameters
        # Note: In Morpho Blue schema, inputToken is collateral, borrowedToken is loan token
        params = {
            "loan_token": graphql_market.get("borrowedToken", {}).get("id", ""),
            "collateral_token": graphql_market.get("inputToken", {}).get("id", ""),
            "irm": graphql_market.get("irm", ""),
            "oracle": graphql_market.get("oracle", ""),
            "lltv": str(graphql_market.get("lltv", "0")),
        }
        
        # Extract market state
        state = {
            "total_supply_assets": str(graphql_market.get("totalSupply", "0")),
            "total_supply_shares": str(graphql_market.get("totalSupplyShares", "0")),
            "total_borrow_assets": str(graphql_market.get("totalBorrow", "0")),
            "total_borrow_shares": str(graphql_market.get("totalBorrowShares", "0")),
            "last_update": str(graphql_market.get("createdTimestamp", "0")),
            "fee": str(graphql_market.get("fee", "0")),
        }
        
        # Construct complete market data
        market_data = {
            "chain_id": str(chain_id),
            "id": graphql_market.get("id", ""),
            "params": params,
            "state": state,
            "cap": str(cap),
            "vault_assets": str(vault_assets),
            "rate_at_target": str(rate_at_target),
        }
        
        logger.debug(f"Transformed market data for market {market_data['id']}")
        return market_data
        
    except Exception as e:
        logger.error(f"Failed to transform market data: {e}")
        raise DataTransformationError(f"Market data transformation failed: {e}")


def transform_market_params(graphql_market: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform GraphQL market data to interface market params format.
    
    Args:
        graphql_market: Market data from GraphQL response
        
    Returns:
        Market parameters in interface format
        
    Raises:
        DataTransformationError: If transformation fails
    """
    try:
        # Note: In Morpho Blue schema, inputToken is collateral, borrowedToken is loan token
        params = {
            "loan_token": graphql_market.get("borrowedToken", {}).get("id", ""),
            "collateral_token": graphql_market.get("inputToken", {}).get("id", ""),
            "irm": graphql_market.get("irm", ""),
            "oracle": graphql_market.get("oracle", ""),
            "lltv": str(graphql_market.get("lltv", "0")),
        }
        
        logger.debug(f"Transformed market params for market {graphql_market.get('id', 'unknown')}")
        return params
        
    except Exception as e:
        logger.error(f"Failed to transform market params: {e}")
        raise DataTransformationError(f"Market params transformation failed: {e}")


def transform_market_state(graphql_market: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform GraphQL market data to interface market state format.
    
    Args:
        graphql_market: Market data from GraphQL response
        
    Returns:
        Market state in interface format
        
    Raises:
        DataTransformationError: If transformation fails
    """
    try:
        state = {
            "total_supply_assets": str(graphql_market.get("totalSupply", "0")),
            "total_supply_shares": str(graphql_market.get("totalSupplyShares", "0")),
            "total_borrow_assets": str(graphql_market.get("totalBorrow", "0")),
            "total_borrow_shares": str(graphql_market.get("totalBorrowShares", "0")),
            "last_update": str(graphql_market.get("createdTimestamp", "0")),
            "fee": str(graphql_market.get("fee", "0")),
        }
        
        logger.debug(f"Transformed market state for market {graphql_market.get('id', 'unknown')}")
        return state
        
    except Exception as e:
        logger.error(f"Failed to transform market state: {e}")
        raise DataTransformationError(f"Market state transformation failed: {e}")


def transform_vault_data(graphql_vault: Dict[str, Any], chain_id: int) -> Dict[str, Any]:
    """
    Transform GraphQL vault data to interface format.
    
    Args:
        graphql_vault: Vault data from GraphQL response
        chain_id: Chain ID for the vault
        
    Returns:
        Vault data in interface format with market_data array
        
    Raises:
        DataTransformationError: If transformation fails
    """
    try:
        vault_address = graphql_vault.get("id", "")
        
        # Extract markets from withdraw queue (this contains all vault markets)
        market_data_list = []
        
        # Process withdraw queue markets (primary source of market data)
        withdraw_queue = graphql_vault.get("withdrawQueue", [])
        for queue_item in withdraw_queue:
            market = queue_item.get("market", {})
            if market and market.get("id"):
                # Transform market data with vault-specific information
                market_data = transform_market_data(
                    graphql_market=market,
                    chain_id=chain_id,
                    vault_assets="0",  # TODO: Get actual vault assets from positions
                    cap="0",  # TODO: Get actual cap from vault config
                    rate_at_target="0"  # TODO: Get actual rate
                )
                market_data_list.append(market_data)
        
        # If no markets in withdraw queue, try supply queue
        if not market_data_list:
            supply_queue = graphql_vault.get("supplyQueue", [])
            for queue_item in supply_queue:
                market = queue_item.get("market", {})
                if market and market.get("id"):
                    market_data = transform_market_data(
                        graphql_market=market,
                        chain_id=chain_id,
                        vault_assets="0",
                        cap="0",
                        rate_at_target="0"
                    )
                    market_data_list.append(market_data)
        
        vault_data = {
            "vault_address": vault_address,
            "market_data": market_data_list,
        }
        
        logger.debug(f"Transformed vault data for vault {vault_address} with {len(market_data_list)} markets")
        return vault_data
        
    except Exception as e:
        logger.error(f"Failed to transform vault data: {e}")
        raise DataTransformationError(f"Vault data transformation failed: {e}")


def extract_market_ids_from_vault(graphql_vault: Dict[str, Any]) -> List[str]:
    """
    Extract all market IDs associated with a vault.
    
    Args:
        graphql_vault: Vault data from GraphQL response
        
    Returns:
        List of market IDs
    """
    market_ids = set()
    
    # Extract from withdraw queue
    withdraw_queue = graphql_vault.get("withdrawQueue", [])
    for queue_item in withdraw_queue:
        market = queue_item.get("market", {})
        if market and market.get("id"):
            market_ids.add(market["id"])
    
    # Extract from supply queue
    supply_queue = graphql_vault.get("supplyQueue", [])
    for queue_item in supply_queue:
        market = queue_item.get("market", {})
        if market and market.get("id"):
            market_ids.add(market["id"])
    
    return list(market_ids)


def validate_market_data(market_data: Dict[str, Any]) -> bool:
    """
    Validate that market data conforms to interface requirements.

    Args:
        market_data: Market data to validate

    Returns:
        True if valid, False otherwise
    """
    if not isinstance(market_data, dict):
        logger.error("Market data must be a dictionary")
        return False

    required_fields = [
        "chain_id", "id", "params", "state", "cap", "vault_assets", "rate_at_target"
    ]

    required_param_fields = [
        "loan_token", "collateral_token", "irm", "oracle", "lltv"
    ]

    required_state_fields = [
        "total_supply_assets", "total_supply_shares", "total_borrow_assets",
        "total_borrow_shares", "last_update", "fee"
    ]

    # Check top-level fields
    for field in required_fields:
        if field not in market_data:
            logger.error(f"Missing required field: {field}")
            return False

        # Validate field types
        if field in ["chain_id", "id", "cap", "vault_assets", "rate_at_target"]:
            if not isinstance(market_data[field], str):
                logger.error(f"Field {field} must be a string, got {type(market_data[field])}")
                return False
    
    # Check params fields
    params = market_data.get("params", {})
    if not isinstance(params, dict):
        logger.error("params must be a dictionary")
        return False

    for field in required_param_fields:
        if field not in params:
            logger.error(f"Missing required params field: {field}")
            return False
        if not isinstance(params[field], str):
            logger.error(f"Param field {field} must be a string, got {type(params[field])}")
            return False

    # Check state fields
    state = market_data.get("state", {})
    if not isinstance(state, dict):
        logger.error("state must be a dictionary")
        return False

    for field in required_state_fields:
        if field not in state:
            logger.error(f"Missing required state field: {field}")
            return False
        if not isinstance(state[field], str):
            logger.error(f"State field {field} must be a string, got {type(state[field])}")
            return False

    # Validate that numeric fields contain valid values
    numeric_fields = ["lltv"]
    for field in numeric_fields:
        if field in params:
            try:
                float(params[field])
            except (ValueError, TypeError):
                logger.error(f"Param field {field} must be a valid numeric string")
                return False

    numeric_state_fields = ["total_supply_assets", "total_supply_shares",
                           "total_borrow_assets", "total_borrow_shares", "fee"]
    for field in numeric_state_fields:
        if field in state:
            try:
                float(state[field])
            except (ValueError, TypeError):
                logger.error(f"State field {field} must be a valid numeric string")
                return False

    return True


def validate_vault_data(vault_data: Dict[str, Any]) -> bool:
    """
    Validate that vault data conforms to interface requirements.

    Args:
        vault_data: Vault data to validate

    Returns:
        True if valid, False otherwise
    """
    if not isinstance(vault_data, dict):
        logger.error("Vault data must be a dictionary")
        return False

    required_fields = ["vault_address", "market_data"]

    # Check top-level fields
    for field in required_fields:
        if field not in vault_data:
            logger.error(f"Missing required vault field: {field}")
            return False

    # Validate vault_address
    vault_address = vault_data.get("vault_address", "")
    if not isinstance(vault_address, str) or not vault_address:
        logger.error("vault_address must be a non-empty string")
        return False

    # Validate each market in market_data
    market_data_list = vault_data.get("market_data", [])
    if not isinstance(market_data_list, list):
        logger.error("market_data must be a list")
        return False

    for i, market_data in enumerate(market_data_list):
        # Handle both dict format and MarketData objects
        if hasattr(market_data, 'fetch_market_data'):
            # This is a MarketData object, which is valid according to interface
            continue
        elif isinstance(market_data, dict):
            # This is a dict, validate it
            if not validate_market_data(market_data):
                logger.error(f"Invalid market data at index {i}")
                return False
        else:
            logger.error(f"Market data at index {i} must be either dict or MarketData object")
            return False

    logger.debug(f"Validated vault data with {len(market_data_list)} markets")
    return True
