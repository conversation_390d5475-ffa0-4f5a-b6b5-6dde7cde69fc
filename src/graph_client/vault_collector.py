"""
Vault data collection from The Graph GraphQL endpoint.

This module provides functions to collect vault information including supply/withdraw
queues and associated market data while maintaining strict interface compliance.
"""

from log import get_logger
from typing import Dict, Any, List, Optional

from .client import GraphQLClient, create_client
from .queries import (
    GET_VAULT_BY_ADDRESS,
    GET_VAULT_WITH_MARKETS,
    GET_VAULT_POSITIONS
)
from .transformers import (
    transform_vault_data,
    transform_market_data,
    extract_market_ids_from_vault,
    validate_vault_data,
    DataTransformationError
)
from .types import (
    VaultResponse,
    PositionsResponse,
    VaultData
)
from config.config import PRIMARY_CHAIN_ID

logger = get_logger(__name__)


class VaultDataCollector:
    """
    Collector for vault data from The Graph GraphQL endpoint.
    
    Provides methods to fetch vault information, supply/withdraw queues,
    and associated market data while ensuring strict interface compliance.
    """
    
    def __init__(self, chain_id: int = PRIMARY_CHAIN_ID):
        """
        Initialize vault data collector.
        
        Args:
            chain_id: Chain ID to collect data from (default: Base)
        """
        self.chain_id = chain_id
        self.client = create_client(chain_id)
        logger.info(f"Initialized VaultDataCollector for chain {chain_id}")
    
    def fetch_vault_data(self, vault_address: str) -> Dict[str, Any]:
        """
        Fetch complete vault data including all associated markets.
        
        This method implements the IVaultData.fetch_vault_data interface.
        
        Args:
            vault_address: The vault address to fetch data for
            
        Returns:
            Complete vault data with market_data array in interface format
            
        Raises:
            Exception: If data collection or transformation fails
        """
        try:
            logger.info(f"Fetching vault data for vault {vault_address}")
            
            # Execute GraphQL query to get vault with embedded market data
            variables = {"vaultAddress": vault_address.lower()}
            response_data = self.client.execute_query(GET_VAULT_WITH_MARKETS, variables)
            
            # Parse response
            vault_response: VaultResponse = response_data
            graphql_vault = vault_response.get("metaMorpho")
            
            if not graphql_vault:
                raise ValueError(f"Vault {vault_address} not found")
            
            # Transform to interface format
            vault_data = transform_vault_data(
                graphql_vault=graphql_vault,
                chain_id=self.chain_id
            )
            
            # Enhance market data with vault-specific information
            vault_data = self._enhance_vault_market_data(vault_data, vault_address)
            
            # Validate compliance
            if not validate_vault_data(vault_data):
                raise DataTransformationError("Vault data validation failed")
            
            logger.info(f"Successfully fetched vault data for {vault_address} with {len(vault_data['market_data'])} markets")
            return vault_data
            
        except Exception as e:
            logger.error(f"Failed to fetch vault data for {vault_address}: {e}")
            raise
    
    def _enhance_vault_market_data(self, vault_data: Dict[str, Any], vault_address: str) -> Dict[str, Any]:
        """
        Enhance market data with vault-specific information like positions and caps.
        
        Args:
            vault_data: Base vault data
            vault_address: Vault address
            
        Returns:
            Enhanced vault data with vault-specific market information
        """
        try:
            market_data_list = vault_data.get("market_data", [])
            if not market_data_list:
                return vault_data
            
            # Extract market IDs
            market_ids = [market["id"] for market in market_data_list]
            
            # Get vault positions for these markets
            positions = self._get_vault_positions(vault_address, market_ids)
            
            # Create position lookup
            position_lookup = {pos.get("market", {}).get("id", ""): pos for pos in positions}
            
            # Enhance each market with vault-specific data
            enhanced_markets = []
            for market_data in market_data_list:
                market_id = market_data["id"]
                position = position_lookup.get(market_id, {})
                
                # Update vault_assets with actual position balance
                vault_assets = position.get("balance", "0")
                market_data["vault_assets"] = str(vault_assets)

                # Get cap from vault configuration if available
                # Note: Cap information may not be directly available in the subgraph
                # This would typically come from on-chain vault configuration
                market_data["cap"] = "0"  # Placeholder until on-chain integration
                
                enhanced_markets.append(market_data)
            
            vault_data["market_data"] = enhanced_markets
            return vault_data
            
        except Exception as e:
            logger.warning(f"Failed to enhance vault market data: {e}")
            # Return original data if enhancement fails
            return vault_data
    
    def _get_vault_positions(self, vault_address: str, market_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Get vault positions for specific markets.
        
        Args:
            vault_address: Vault address
            market_ids: List of market IDs
            
        Returns:
            List of position data
        """
        try:
            if not market_ids:
                return []
            
            variables = {
                "vaultAddress": vault_address.lower(),
                "marketIds": market_ids
            }
            response_data = self.client.execute_query(GET_VAULT_POSITIONS, variables)
            
            positions_response: PositionsResponse = response_data
            return positions_response.get("positions", [])
            
        except Exception as e:
            logger.warning(f"Failed to get vault positions: {e}")
            return []
    
    def get_vault_markets(self, vault_address: str) -> List[str]:
        """
        Get list of market IDs associated with a vault.
        
        Args:
            vault_address: Vault address
            
        Returns:
            List of market IDs
        """
        try:
            logger.info(f"Getting market IDs for vault {vault_address}")
            
            variables = {"vaultAddress": vault_address.lower()}
            response_data = self.client.execute_query(GET_VAULT_BY_ADDRESS, variables)
            
            vault_response: VaultResponse = response_data
            graphql_vault = vault_response.get("metaMorpho")
            
            if not graphql_vault:
                raise ValueError(f"Vault {vault_address} not found")
            
            market_ids = extract_market_ids_from_vault(graphql_vault)
            
            logger.info(f"Found {len(market_ids)} markets for vault {vault_address}")
            return market_ids
            
        except Exception as e:
            logger.error(f"Failed to get vault markets for {vault_address}: {e}")
            raise
    
    def get_vault_supply_queue(self, vault_address: str) -> List[str]:
        """
        Get vault's supply queue (ordered list of markets for deposits).
        
        Args:
            vault_address: Vault address
            
        Returns:
            List of market IDs in supply queue order
        """
        try:
            variables = {"vaultAddress": vault_address.lower()}
            response_data = self.client.execute_query(GET_VAULT_BY_ADDRESS, variables)
            
            vault_response: VaultResponse = response_data
            graphql_vault = vault_response.get("metaMorpho")
            
            if not graphql_vault:
                raise ValueError(f"Vault {vault_address} not found")
            
            supply_queue = graphql_vault.get("supplyQueue", [])
            market_ids = [item.get("market", {}).get("id", "") for item in supply_queue if item.get("market", {}).get("id")]
            
            logger.info(f"Found {len(market_ids)} markets in supply queue for vault {vault_address}")
            return market_ids
            
        except Exception as e:
            logger.error(f"Failed to get supply queue for {vault_address}: {e}")
            raise
    
    def get_vault_withdraw_queue(self, vault_address: str) -> List[str]:
        """
        Get vault's withdraw queue (ordered list of markets for withdrawals).
        
        Args:
            vault_address: Vault address
            
        Returns:
            List of market IDs in withdraw queue order
        """
        try:
            variables = {"vaultAddress": vault_address.lower()}
            response_data = self.client.execute_query(GET_VAULT_BY_ADDRESS, variables)
            
            vault_response: VaultResponse = response_data
            graphql_vault = vault_response.get("metaMorpho")
            
            if not graphql_vault:
                raise ValueError(f"Vault {vault_address} not found")
            
            withdraw_queue = graphql_vault.get("withdrawQueue", [])
            market_ids = [item.get("market", {}).get("id", "") for item in withdraw_queue if item.get("market", {}).get("id")]
            
            logger.info(f"Found {len(market_ids)} markets in withdraw queue for vault {vault_address}")
            return market_ids
            
        except Exception as e:
            logger.error(f"Failed to get withdraw queue for {vault_address}: {e}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test connection to The Graph endpoint.
        
        Returns:
            True if connection is successful
        """
        return self.client.test_connection()


# Factory function for easy instantiation
def create_vault_collector(chain_id: int = PRIMARY_CHAIN_ID) -> VaultDataCollector:
    """
    Create a vault data collector for a specific chain.
    
    Args:
        chain_id: Chain ID to create collector for
        
    Returns:
        Configured VaultDataCollector instance
    """
    return VaultDataCollector(chain_id)
