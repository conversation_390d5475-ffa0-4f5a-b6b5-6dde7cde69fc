"""
Market data collection from The Graph GraphQL endpoint.

This module provides functions to collect market parameters, state, and liquidity data
from Morpho Blue subgraphs while maintaining strict interface compliance.
"""

from log import get_logger
from typing import Dict, Any, List, Optional

from .client import GraphQLClient, create_client
from .queries import (
    GET_MARKET_BY_ID,
    GET_MARKETS_BATCH,
    GET_MARKET_INTEREST_RATES
)
from .transformers import (
    transform_market_data,
    transform_market_params,
    transform_market_state,
    validate_market_data,
    DataTransformationError
)
from .types import (
    MarketResponse,
    MarketsResponse,
    InterestRatesResponse,
    MarketData,
    MarketParams,
    MarketState
)
from config.config import PRIMARY_CHAIN_ID

logger = get_logger(__name__)


class MarketDataCollector:
    """
    Collector for market data from The Graph GraphQL endpoint.
    
    Provides methods to fetch market parameters, state, and complete market data
    while ensuring strict compliance with interface requirements.
    """
    
    def __init__(self, chain_id: int = PRIMARY_CHAIN_ID):
        """
        Initialize market data collector.
        
        Args:
            chain_id: Chain ID to collect data from (default: Base)
        """
        self.chain_id = chain_id
        self.client = create_client(chain_id)
        logger.info(f"Initialized MarketDataCollector for chain {chain_id}")
    
    def fetch_market_data(self, market_id: str) -> Dict[str, Any]:
        """
        Fetch complete market data for a specific market.
        
        This method implements the IMarketData.fetch_market_data interface.
        
        Args:
            market_id: The market ID to fetch data for
            
        Returns:
            Complete market data in interface format
            
        Raises:
            Exception: If data collection or transformation fails
        """
        try:
            logger.info(f"Fetching market data for market {market_id}")
            
            # Execute GraphQL query
            variables = {"marketId": market_id}
            response_data = self.client.execute_query(GET_MARKET_BY_ID, variables)
            
            # Parse response
            market_response: MarketResponse = response_data
            graphql_market = market_response.get("market")
            
            if not graphql_market:
                raise ValueError(f"Market {market_id} not found")
            
            # Get interest rates for this market
            rate_at_target = self._get_current_interest_rate(market_id)
            
            # Transform to interface format
            market_data = transform_market_data(
                graphql_market=graphql_market,
                chain_id=self.chain_id,
                vault_assets="0",  # Will be populated by vault-specific queries
                cap="0",  # Will be populated by vault-specific queries
                rate_at_target=rate_at_target
            )
            
            # Validate compliance
            if not validate_market_data(market_data):
                raise DataTransformationError("Market data validation failed")
            
            logger.info(f"Successfully fetched market data for {market_id}")
            return market_data
            
        except Exception as e:
            logger.error(f"Failed to fetch market data for {market_id}: {e}")
            raise
    
    def fetch_market_params(self, market_id: str) -> Dict[str, Any]:
        """
        Fetch market parameters for a specific market.
        
        This method implements the IMarketData.fetch_market_params interface.
        
        Args:
            market_id: The market ID to fetch parameters for
            
        Returns:
            Market parameters in interface format
            
        Raises:
            Exception: If data collection or transformation fails
        """
        try:
            logger.info(f"Fetching market params for market {market_id}")
            
            # Execute GraphQL query
            variables = {"marketId": market_id}
            response_data = self.client.execute_query(GET_MARKET_BY_ID, variables)
            
            # Parse response
            market_response: MarketResponse = response_data
            graphql_market = market_response.get("market")
            
            if not graphql_market:
                raise ValueError(f"Market {market_id} not found")
            
            # Transform to interface format
            market_params = transform_market_params(graphql_market)
            
            logger.info(f"Successfully fetched market params for {market_id}")
            return market_params
            
        except Exception as e:
            logger.error(f"Failed to fetch market params for {market_id}: {e}")
            raise
    
    def fetch_market_state(self, market_id: str) -> Dict[str, Any]:
        """
        Fetch market state for a specific market.
        
        This method implements the IMarketData.fetch_market_state interface.
        
        Args:
            market_id: The market ID to fetch state for
            
        Returns:
            Market state in interface format
            
        Raises:
            Exception: If data collection or transformation fails
        """
        try:
            logger.info(f"Fetching market state for market {market_id}")
            
            # Execute GraphQL query
            variables = {"marketId": market_id}
            response_data = self.client.execute_query(GET_MARKET_BY_ID, variables)
            
            # Parse response
            market_response: MarketResponse = response_data
            graphql_market = market_response.get("market")
            
            if not graphql_market:
                raise ValueError(f"Market {market_id} not found")
            
            # Transform to interface format
            market_state = transform_market_state(graphql_market)
            
            logger.info(f"Successfully fetched market state for {market_id}")
            return market_state
            
        except Exception as e:
            logger.error(f"Failed to fetch market state for {market_id}: {e}")
            raise
    
    def fetch_markets_batch(self, market_ids: List[str], include_rates: bool = False) -> List[Dict[str, Any]]:
        """
        Fetch market data for multiple markets in a single query.

        Args:
            market_ids: List of market IDs to fetch
            include_rates: Whether to fetch interest rates (slower but more complete)

        Returns:
            List of market data in interface format

        Raises:
            Exception: If data collection or transformation fails
        """
        try:
            logger.info(f"Fetching batch market data for {len(market_ids)} markets")

            if not market_ids:
                return []

            # Limit batch size to avoid query timeouts
            batch_size = 10
            all_market_data = []

            for i in range(0, len(market_ids), batch_size):
                batch_ids = market_ids[i:i + batch_size]
                logger.debug(f"Processing batch {i//batch_size + 1}: {len(batch_ids)} markets")

                # Execute GraphQL query
                variables = {"marketIds": batch_ids}
                response_data = self.client.execute_query(GET_MARKETS_BATCH, variables)

                # Parse response
                markets_response: MarketsResponse = response_data
                graphql_markets = markets_response.get("markets", [])

                # Transform each market
                for graphql_market in graphql_markets:
                    market_id = graphql_market.get("id", "")

                    # Only fetch rates if explicitly requested (performance optimization)
                    rate_at_target = "0"
                    if include_rates:
                        rate_at_target = self._get_current_interest_rate(market_id)

                    market_data = transform_market_data(
                        graphql_market=graphql_market,
                        chain_id=self.chain_id,
                        vault_assets="0",
                        cap="0",
                        rate_at_target=rate_at_target
                    )

                    if validate_market_data(market_data):
                        all_market_data.append(market_data)
                    else:
                        logger.warning(f"Skipping invalid market data for {market_id}")

            logger.info(f"Successfully fetched {len(all_market_data)} markets in batches")
            return all_market_data

        except Exception as e:
            logger.error(f"Failed to fetch batch market data: {e}")
            raise
    
    def _get_current_interest_rate(self, market_id: str) -> str:
        """
        Get current interest rate for a market.

        Args:
            market_id: Market ID to get rate for

        Returns:
            Current interest rate as string
        """
        try:
            if not market_id or market_id == "":
                logger.warning("Empty market_id provided for interest rate query")
                return "0"

            variables = {"marketId": market_id}
            response_data = self.client.execute_query(GET_MARKET_INTEREST_RATES, variables)

            rates_response: InterestRatesResponse = response_data
            interest_rates = rates_response.get("interestRates", [])

            if not interest_rates:
                logger.debug(f"No interest rates found for market {market_id}")
                return "0"

            # Find the most recent lender (supply) rate
            for rate in interest_rates:
                if rate.get("side") == "LENDER":
                    rate_value = rate.get("rate", "0")
                    logger.debug(f"Found lender rate {rate_value} for market {market_id}")
                    return str(rate_value)

            # Fallback to any rate if no lender rate found
            if interest_rates:
                rate_value = interest_rates[0].get("rate", "0")
                logger.debug(f"Using fallback rate {rate_value} for market {market_id}")
                return str(rate_value)

            return "0"

        except Exception as e:
            logger.warning(f"Failed to get interest rate for {market_id}: {e}")
            return "0"
    
    def test_connection(self) -> bool:
        """
        Test connection to The Graph endpoint.
        
        Returns:
            True if connection is successful
        """
        return self.client.test_connection()


# Factory function for easy instantiation
def create_market_collector(chain_id: int = PRIMARY_CHAIN_ID) -> MarketDataCollector:
    """
    Create a market data collector for a specific chain.
    
    Args:
        chain_id: Chain ID to create collector for
        
    Returns:
        Configured MarketDataCollector instance
    """
    return MarketDataCollector(chain_id)
