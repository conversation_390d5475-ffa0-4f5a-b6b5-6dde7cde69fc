"""
GraphQL Client for The Graph integration with Morpho Blue subgraphs.

This client handles authentication, retries, and error handling for GraphQL queries
to The Graph's Morpho Blue subgraphs across different chains.
"""

import json
import time
from log import get_logger
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from config.config import (
    THE_GRAPH_API_KEY,
    GRAPHQL_CONFIG,
    get_query_url,
    get_chain_config,
    PRIMARY_CHAIN_ID
)

logger = get_logger(__name__)


class GraphQLError(Exception):
    """Custom exception for GraphQL-related errors."""
    pass


class GraphQLClient:
    """
    GraphQL client for The Graph Morpho Blue subgraphs.
    
    Handles authentication, retries, and error handling for GraphQL queries.
    Supports multiple chains with automatic endpoint resolution.
    """
    
    def __init__(self, chain_id: int = PRIMARY_CHAIN_ID):
        """
        Initialize GraphQL client for a specific chain.
        
        Args:
            chain_id: The chain ID to query (default: Base chain)
        """
        self.chain_id = chain_id
        self.chain_config = get_chain_config(chain_id)
        self.query_url = get_query_url(chain_id)
        
        if not self.chain_config:
            raise ValueError(f"Unsupported chain ID: {chain_id}")
        
        if not THE_GRAPH_API_KEY:
            raise ValueError("THE_GRAPH_API_KEY environment variable is required")
        
        if not self.query_url:
            raise ValueError(f"Could not construct query URL for chain {chain_id}")
        
        # Setup HTTP session with retries
        self.session = requests.Session()
        retry_strategy = Retry(
            total=GRAPHQL_CONFIG["max_retries"],
            backoff_factor=GRAPHQL_CONFIG["retry_delay"],
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "morpho-reallocator-bot/1.0"
        })
        
        logger.info(f"Initialized GraphQL client for {self.chain_config['name']} (Chain ID: {chain_id})")
    
    def execute_query(self, query: str, variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a GraphQL query against The Graph endpoint.
        
        Args:
            query: The GraphQL query string
            variables: Optional variables for the query
            
        Returns:
            The GraphQL response data
            
        Raises:
            GraphQLError: If the query fails or returns errors
        """
        payload = {
            "query": query,
            "variables": variables or {}
        }
        
        try:
            logger.debug(f"Executing GraphQL query: {query[:100]}...")
            
            response = self.session.post(
                self.query_url,
                json=payload,
                timeout=GRAPHQL_CONFIG["timeout"]
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Check for GraphQL errors
            if "errors" in data:
                error_messages = [error.get("message", "Unknown error") for error in data["errors"]]
                raise GraphQLError(f"GraphQL errors: {', '.join(error_messages)}")
            
            if "data" not in data:
                raise GraphQLError("No data field in GraphQL response")
            
            logger.debug(f"GraphQL query executed successfully")
            return data["data"]
            
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request failed: {e}")
            raise GraphQLError(f"HTTP request failed: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response: {e}")
            raise GraphQLError(f"Invalid JSON response: {e}")
        except Exception as e:
            logger.error(f"Unexpected error executing GraphQL query: {e}")
            raise GraphQLError(f"Unexpected error: {e}")
    
    def get_chain_info(self) -> Dict[str, Any]:
        """Get information about the current chain configuration."""
        return {
            "chain_id": self.chain_id,
            "chain_name": self.chain_config["name"],
            "subgraph_id": self.chain_config["subgraph_id"],
            "query_url": self.query_url
        }
    
    def test_connection(self) -> bool:
        """
        Test the connection to The Graph endpoint.
        
        Returns:
            True if connection is successful, False otherwise
        """
        test_query = """
        query TestConnection {
            markets(first: 1) {
                id
            }
        }
        """
        
        try:
            result = self.execute_query(test_query)
            logger.info(f"Connection test successful for {self.chain_config['name']}")
            return True
        except Exception as e:
            logger.error(f"Connection test failed for {self.chain_config['name']}: {e}")
            return False


def create_client(chain_id: int = PRIMARY_CHAIN_ID) -> GraphQLClient:
    """
    Factory function to create a GraphQL client for a specific chain.
    
    Args:
        chain_id: The chain ID to create a client for
        
    Returns:
        Configured GraphQL client instance
    """
    return GraphQLClient(chain_id)
