"""
GraphQL queries for Morpho Blue subgraph data.

This module contains all GraphQL queries needed to fetch market and vault data
from The Graph's Morpho Blue subgraphs.
"""

# Market-related queries

GET_MARKET_BY_ID = """
query GetMarketById($marketId: String!) {
    market(id: $marketId) {
        id
        name
        inputToken {
            id
            name
            symbol
            decimals
        }
        borrowedToken {
            id
            name
            symbol
            decimals
        }
        oracle
        irm
        lltv
        totalSupply
        totalSupplyShares
        totalBorrow
        totalBorrowShares
        totalBorrowBalanceUSD
        totalDepositBalanceUSD
        totalValueLockedUSD
        inputTokenBalance
        inputTokenPriceUSD
        fee
        isActive
        createdTimestamp
        createdBlockNumber
    }
}
"""

GET_MARKETS_BATCH = """
query GetMarketsBatch($marketIds: [String!]!) {
    markets(where: { id_in: $marketIds }) {
        id
        name
        inputToken {
            id
            name
            symbol
            decimals
        }
        borrowedToken {
            id
            name
            symbol
            decimals
        }
        oracle
        irm
        lltv
        totalSupply
        totalSupplyShares
        totalBorrow
        totalBorrowShares
        totalBorrowBalanceUSD
        totalDepositBalanceUSD
        totalValueLockedUSD
        inputTokenBalance
        inputTokenPriceUSD
        fee
        isActive
        createdTimestamp
        createdBlockNumber
    }
}
"""

GET_MARKET_INTEREST_RATES = """
query GetMarketInterestRates($marketId: String!) {
    interestRates(
        where: { market: $marketId }
        orderBy: rate
        orderDirection: desc
        first: 2
    ) {
        id
        rate
        side
        market {
            id
        }
    }
}
"""

# Vault (MetaMorpho) related queries

GET_VAULT_BY_ADDRESS = """
query GetVaultByAddress($vaultAddress: String!) {
    metaMorpho(id: $vaultAddress) {
        id
        name
        symbol
        asset {
            id
            name
            symbol
            decimals
        }
        owner {
            id
        }
        curator {
            id
        }
        guardian {
            id
        }
        fee
        feeRecipient {
            id
        }
        timelock
        lastTotalAssets
        supplyQueue {
            id
            market {
                id
            }
        }
        withdrawQueue {
            id
            market {
                id
            }
        }
        allocators {
            id
            account {
                id
            }
            isCurrentAllocator
        }
    }
}
"""

GET_VAULT_POSITIONS = """
query GetVaultPositions($vaultAddress: String!, $marketIds: [String!]!) {
    positions(
        where: { 
            account: $vaultAddress,
            market_in: $marketIds,
            side: LENDER
        }
    ) {
        id
        account {
            id
        }
        market {
            id
        }
        asset {
            id
        }
        side
        balance
        shares
        principal
        depositCount
        withdrawCount
        isCollateral
    }
}
"""

# Combined queries for efficiency

GET_VAULT_WITH_MARKETS = """
query GetVaultWithMarkets($vaultAddress: String!) {
    metaMorpho(id: $vaultAddress) {
        id
        name
        symbol
        asset {
            id
            name
            symbol
            decimals
        }
        owner {
            id
        }
        curator {
            id
        }
        guardian {
            id
        }
        fee
        feeRecipient {
            id
        }
        timelock
        lastTotalAssets
        supplyQueue {
            id
            market {
                id
                name
                inputToken {
                    id
                    name
                    symbol
                    decimals
                }
                borrowedToken {
                    id
                    name
                    symbol
                    decimals
                }
                oracle
                irm
                lltv
                totalSupply
                totalSupplyShares
                totalBorrow
                totalBorrowShares
                totalBorrowBalanceUSD
                totalDepositBalanceUSD
                totalValueLockedUSD
                inputTokenBalance
                inputTokenPriceUSD
                fee
                isActive
            }
        }
        withdrawQueue {
            id
            market {
                id
                name
                inputToken {
                    id
                    name
                    symbol
                    decimals
                }
                borrowedToken {
                    id
                    name
                    symbol
                    decimals
                }
                oracle
                irm
                lltv
                totalSupply
                totalSupplyShares
                totalBorrow
                totalBorrowShares
                totalBorrowBalanceUSD
                totalDepositBalanceUSD
                totalValueLockedUSD
                inputTokenBalance
                inputTokenPriceUSD
                fee
                isActive
            }
        }
    }
}
"""

# Utility queries

GET_PROTOCOL_STATS = """
query GetProtocolStats {
    protocols(first: 1) {
        id
        name
        schemaVersion
        subgraphVersion
        methodologyVersion
        totalValueLockedUSD
        totalBorrowBalanceUSD
        totalDepositBalanceUSD
        cumulativeBorrowUSD
        cumulativeDepositUSD
        marketCount
        openPositionCount
        cumulativeUniqueUsers
    }
}
"""

TEST_QUERY = """
query TestQuery {
    markets(first: 1) {
        id
        name
    }
}
"""
