"""
Type definitions for GraphQL responses and interface data structures.

This module defines TypedDict classes that match both the GraphQL schema
and the interface requirements for type safety and documentation.
"""

from typing import TypedDict, List, Optional, Any, Dict


# GraphQL Response Types (from The Graph)

class GraphQLToken(TypedDict):
    """Token information from GraphQL response."""
    id: str
    name: str
    symbol: str
    decimals: int


class GraphQLAccount(TypedDict):
    """Account information from GraphQL response."""
    id: str


class GraphQLMarket(TypedDict):
    """Market information from GraphQL response."""
    id: str
    name: str
    inputToken: GraphQLToken
    borrowedToken: GraphQLToken
    collateralToken: GraphQLToken
    oracle: str
    irm: str
    lltv: str
    liquidationThreshold: str
    totalSupply: str
    totalSupplyShares: str
    totalBorrow: str
    totalBorrowShares: str
    totalBorrowBalanceUSD: str
    totalDepositBalanceUSD: str
    totalValueLockedUSD: str
    inputTokenBalance: str
    inputTokenPriceUSD: str
    fee: str
    reserveFactor: str
    isActive: bool
    createdTimestamp: str
    createdBlockNumber: str


class GraphQLQueueItem(TypedDict):
    """Queue item from GraphQL response."""
    id: str
    market: GraphQLMarket


class GraphQLAllocator(TypedDict):
    """Allocator information from GraphQL response."""
    id: str
    account: GraphQLAccount
    isCurrentAllocator: bool


class GraphQLVault(TypedDict):
    """Vault (MetaMorpho) information from GraphQL response."""
    id: str
    name: str
    symbol: str
    asset: GraphQLToken
    owner: GraphQLAccount
    curator: Optional[GraphQLAccount]
    guardian: Optional[GraphQLAccount]
    fee: str
    feeRecipient: GraphQLAccount
    timelock: str
    lastTotalAssets: str
    createdTimestamp: str
    createdBlockNumber: str
    supplyQueue: List[GraphQLQueueItem]
    withdrawQueue: List[GraphQLQueueItem]
    allocators: List[GraphQLAllocator]


class GraphQLPosition(TypedDict):
    """Position information from GraphQL response."""
    id: str
    account: GraphQLAccount
    market: GraphQLMarket
    asset: GraphQLToken
    side: str
    balance: str
    shares: str
    principal: str
    depositCount: int
    withdrawCount: int
    isCollateral: bool


class GraphQLInterestRate(TypedDict):
    """Interest rate information from GraphQL response."""
    id: str
    rate: str
    side: str
    timestamp: str
    market: GraphQLMarket


# Interface-compliant Types (matching src/interfaces/)

class MarketParams(TypedDict):
    """Market parameters matching interface format."""
    loan_token: str
    collateral_token: str
    irm: str
    oracle: str
    lltv: str


class MarketState(TypedDict):
    """Market state matching interface format."""
    total_supply_assets: str
    total_supply_shares: str
    total_borrow_assets: str
    total_borrow_shares: str
    last_update: str
    fee: str


class MarketData(TypedDict):
    """Complete market data matching interface format."""
    chain_id: str
    id: str
    params: MarketParams
    state: MarketState
    cap: str
    vault_assets: str
    rate_at_target: str


class VaultData(TypedDict):
    """Vault data matching interface format."""
    vault_address: str
    market_data: List[MarketData]


# Utility Types

class ChainConfig(TypedDict):
    """Chain configuration."""
    name: str
    subgraph_id: str
    query_url: str


class GraphQLResponse(TypedDict):
    """Generic GraphQL response structure."""
    data: Optional[Dict[str, Any]]
    errors: Optional[List[Dict[str, Any]]]


# Query Variable Types

class MarketQueryVars(TypedDict):
    """Variables for market queries."""
    marketId: str


class MarketsQueryVars(TypedDict):
    """Variables for batch market queries."""
    marketIds: List[str]


class VaultQueryVars(TypedDict):
    """Variables for vault queries."""
    vaultAddress: str


class VaultPositionsQueryVars(TypedDict):
    """Variables for vault positions queries."""
    vaultAddress: str
    marketIds: List[str]


# Response wrapper types for specific queries

class MarketResponse(TypedDict):
    """Response for single market query."""
    market: Optional[GraphQLMarket]


class MarketsResponse(TypedDict):
    """Response for batch markets query."""
    markets: List[GraphQLMarket]


class VaultResponse(TypedDict):
    """Response for vault query."""
    metaMorpho: Optional[GraphQLVault]


class PositionsResponse(TypedDict):
    """Response for positions query."""
    positions: List[GraphQLPosition]


class InterestRatesResponse(TypedDict):
    """Response for interest rates query."""
    interestRates: List[GraphQLInterestRate]
