import time
from typing import Dict, Any, Optional
from log import get_logger

from interfaces.market_interface import IMarketData
from graph_client.market_collector import create_market_collector, MarketDataCollector
from config.config import PRIMARY_CHAIN_ID

logger = get_logger(__name__)

# Cache expiration time in seconds (5 minutes)
CACHE_EXPIRATION_SECONDS = 300


class MarketDataSubgraph(IMarketData):
    """
    Market data implementation using The Graph GraphQL endpoint.

    This class implements the IMarketData interface and provides market data
    collection from Morpho Blue subgraphs via The Graph.
    """

    def __init__(self, market_id: str, chain_id: int = PRIMARY_CHAIN_ID):
        """
        Initialize MarketData instance.

        Args:
            market_id: The market ID to fetch data for
            chain_id: Chain ID to fetch data from (default: Base)
        """
        self.market_id = market_id
        self.chain_id = chain_id
        self.collector: MarketDataCollector = create_market_collector(chain_id)

        # Cache for fetched data with timestamps
        self._data_cache: Optional[Dict[str, Any]] = None
        self._data_cache_time: float = 0
        self._params_cache: Optional[Dict[str, Any]] = None
        self._params_cache_time: float = 0
        self._state_cache: Optional[Dict[str, Any]] = None
        self._state_cache_time: float = 0

        logger.info(f"Initialized MarketData for market {market_id} on chain {chain_id}")

    def fetch_market_data(self, market_id: str) -> dict:
        """
        Fetch complete market data using The Graph GraphQL endpoint.

        This method implements the IMarketData.fetch_market_data interface.
        Returns data in the exact format specified by the interface.

        Args:
            market_id: The market ID to fetch data for

        Returns:
            Complete market data in interface format:
            {
                "chain_id": "",
                "id": "",
                "params": {
                    "loan_token": "",
                    "collateral_token": "",
                    "irm": "",
                    "oracle": "",
                    "lltv": "",
                },
                "state": {
                    "total_supply_assets": "",
                    "total_supply_shares": "",
                    "total_borrow_assets": "",
                    "total_borrow_shares": "",
                    "last_update": "",
                    "fee": "",
                },
                "cap": "",
                "vault_assets": "",
                "rate_at_target": "",
            }
        """
        try:
            # Use cache if available, market_id matches, and cache is not expired
            current_time = time.time()
            if (self._data_cache and
                self.market_id == market_id and
                current_time - self._data_cache_time < CACHE_EXPIRATION_SECONDS):
                logger.debug(f"Returning cached market data for {market_id}")
                return self._data_cache

            # Fetch fresh data
            logger.info(f"Fetching market data for {market_id}")
            market_data = self.collector.fetch_market_data(market_id)

            # Update cache with timestamp
            if self.market_id == market_id:
                self._data_cache = market_data
                self._data_cache_time = current_time

            return market_data

        except Exception as e:
            logger.error(f"Failed to fetch market data for {market_id}: {e}")
            raise

    def fetch_market_params(self, market_id: str) -> dict:
        """
        Fetch market parameters using The Graph GraphQL endpoint.

        This method implements the IMarketData.fetch_market_params interface.
        Returns data in the exact format specified by the interface.

        Args:
            market_id: The market ID to fetch parameters for

        Returns:
            Market parameters in interface format:
            {
                "loan_token": "",
                "collateral_token": "",
                "irm": "",
                "oracle": "",
                "lltv": "",
            }
        """
        try:
            # Use cache if available, market_id matches, and cache is not expired
            current_time = time.time()
            if (self._params_cache and
                self.market_id == market_id and
                current_time - self._params_cache_time < CACHE_EXPIRATION_SECONDS):
                logger.debug(f"Returning cached market params for {market_id}")
                return self._params_cache

            # Fetch fresh data
            logger.info(f"Fetching market params for {market_id}")
            market_params = self.collector.fetch_market_params(market_id)

            # Update cache with timestamp
            if self.market_id == market_id:
                self._params_cache = market_params
                self._params_cache_time = current_time

            return market_params

        except Exception as e:
            logger.error(f"Failed to fetch market params for {market_id}: {e}")
            raise

    def fetch_market_state(self, market_id: str) -> dict:
        """
        Fetch market state using The Graph GraphQL endpoint.

        This method implements the IMarketData.fetch_market_state interface.
        Returns data in the exact format specified by the interface.

        Args:
            market_id: The market ID to fetch state for

        Returns:
            Market state in interface format:
            {
                "total_supply_assets": "",
                "total_supply_shares": "",
                "total_borrow_assets": "",
                "total_borrow_shares": "",
                "last_update": "",
                "fee": "",
            }
        """
        try:
            # Use cache if available, market_id matches, and cache is not expired
            current_time = time.time()
            if (self._state_cache and
                self.market_id == market_id and
                current_time - self._state_cache_time < CACHE_EXPIRATION_SECONDS):
                logger.debug(f"Returning cached market state for {market_id}")
                return self._state_cache

            # Fetch fresh data
            logger.info(f"Fetching market state for {market_id}")
            market_state = self.collector.fetch_market_state(market_id)

            # Update cache with timestamp
            if self.market_id == market_id:
                self._state_cache = market_state
                self._state_cache_time = current_time

            return market_state

        except Exception as e:
            logger.error(f"Failed to fetch market state for {market_id}: {e}")
            raise

    def clear_cache(self) -> None:
        """Clear all cached data to force fresh fetches."""
        self._data_cache = None
        self._data_cache_time = 0
        self._params_cache = None
        self._params_cache_time = 0
        self._state_cache = None
        self._state_cache_time = 0
        logger.debug(f"Cleared cache for market {self.market_id}")

    def is_cache_valid(self, cache_type: str = "data") -> bool:
        """
        Check if a specific cache is valid (not expired).

        Args:
            cache_type: Type of cache to check ("data", "params", "state")

        Returns:
            True if cache is valid, False otherwise
        """
        current_time = time.time()

        if cache_type == "data":
            return (self._data_cache is not None and
                   current_time - self._data_cache_time < CACHE_EXPIRATION_SECONDS)
        elif cache_type == "params":
            return (self._params_cache is not None and
                   current_time - self._params_cache_time < CACHE_EXPIRATION_SECONDS)
        elif cache_type == "state":
            return (self._state_cache is not None and
                   current_time - self._state_cache_time < CACHE_EXPIRATION_SECONDS)
        else:
            return False

    def test_connection(self) -> bool:
        """
        Test connection to The Graph endpoint.

        Returns:
            True if connection is successful
        """
        return self.collector.test_connection()
