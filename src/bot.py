import os
import sys
import json
import traceback

sys.path.append(os.getcwd())

from src.vault_data import VaultData
from config import config
from utils.web3_connector import Web3Connector
from utils.web3_contract import Web3Contract
from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
from utils.fordefi_transaction_helpers import build_and_send_transaction

from log import get_logger

logger = get_logger(__name__)

STRATEGY = PrioritizedUtilizationStrategy


class ReallocationBot:
    def __init__(self):
        # Move heavy initialization to execute_reallocation to avoid init timeouts
        self.strategy = STRATEGY()

    def execute_reallocation(self):
        """Execute reallocation and return result status."""
        try:
            logger.info("Starting reallocation process...")

            # Fetch vault data
            logger.info("Fetching vault data...")
            vault_data = VaultData(
                vault_address=config.VAULT_ADDRESS, chain_id=config.PRIMARY_CHAIN_ID
            ).fetch_vault_data()

            # Get Web3 client
            logger.info("Connecting to Web3...")
            client = Web3Connector(
                provider_url=os.getenv(f"RPC_URL_{config.PRIMARY_CHAIN_ID}", ""),
            ).get_client()

            # Find reallocation strategy
            logger.info("Calculating reallocation strategy...")
            new_allocations = self.strategy.find_reallocation(vault_data)

            # Check if reallocation is needed
            if not new_allocations:
                logger.info("No reallocation needed")
                return {
                    "status": "success",
                    "message": "No reallocation needed",
                    "transaction_hash": None
                }

            # Format parameters to fit the contract function invocation
            params = []
            for new_allocation in new_allocations:
                params.append(
                    tuple(
                        [
                            tuple(list(new_allocation[0].values())),
                            int(new_allocation[1]),
                        ]
                    )
                )

            logger.info(f"Preparing transaction with {len(params)} allocations...")
            contract = Web3Contract(client, config.VAULT_ADDRESS, config.VAULT_ABI)
            wallet_address = config.WALLET_ADDRESS

            function_name = "reallocate"
            params = [params]
            contract_function = getattr(contract.contract.functions, function_name)

            # Execute transaction
            logger.info("Executing reallocation transaction...")
            tx_hash = build_and_send_transaction(
                config.PRIMARY_CHAIN_ID,
                config.FORDEFI_VAULT_ID,
                client,
                contract_function,
                params,
                wallet_address,
            )

            logger.info(f"Transaction hash: {tx_hash}")
            return {
                "status": "success",
                "message": "Reallocation completed successfully",
                "transaction_hash": tx_hash
            }

        except Exception as e:
            error_msg = f"Reallocation failed: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "status": "error",
                "message": error_msg,
                "transaction_hash": None
            }


def lambda_handler(event, context):
    """
    AWS Lambda handler function.

    Args:
        event: Lambda event data (from CloudWatch Events)
        context: Lambda context object

    Returns:
        dict: Response with status and details
    """
    try:
        logger.info(f"Lambda function started. Request ID: {context.aws_request_id}")
        logger.info(f"Remaining time: {context.get_remaining_time_in_millis()}ms")
        logger.info(f"Event: {json.dumps(event)}")

        # Check if we have enough time to execute (need at least 30 seconds)
        if context.get_remaining_time_in_millis() < 30000:
            logger.warning("Insufficient time remaining, skipping execution")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'status': 'skipped',
                    'message': 'Insufficient time remaining for execution',
                    'transaction_hash': None
                }),
                'headers': {
                    'Content-Type': 'application/json'
                }
            }

        # Load cloud environment variables if in AWS (only once per container)
        if os.getenv("cloud_env") and not hasattr(lambda_handler, '_secrets_loaded'):
            logger.info("Loading cloud environment variables...")
            from utils.load_cloud_env import get_secret
            secrets = get_secret(os.getenv("cloud_env"))
            for key, value in secrets.items():
                os.environ[key] = value
            lambda_handler._secrets_loaded = True
            logger.info("Cloud environment variables loaded successfully")
        elif hasattr(lambda_handler, '_secrets_loaded'):
            logger.info("Cloud environment variables already loaded (cached)")

        # Initialize and run bot
        bot = ReallocationBot()
        result = bot.execute_reallocation()

        logger.info(f"Reallocation result: {result}")
        logger.info(f"Execution completed. Remaining time: {context.get_remaining_time_in_millis()}ms")

        # Return proper Lambda response
        return {
            'statusCode': 200 if result['status'] == 'success' else 500,
            'body': json.dumps(result),
            'headers': {
                'Content-Type': 'application/json'
            }
        }

    except Exception as e:
        error_msg = f"Lambda handler failed: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")

        return {
            'statusCode': 500,
            'body': json.dumps({
                'status': 'error',
                'message': error_msg,
                'transaction_hash': None
            }),
            'headers': {
                'Content-Type': 'application/json'
            }
        }


if __name__ == "__main__":
    # For local testing
    bot = ReallocationBot()
    result = bot.execute_reallocation()
    print(json.dumps(result, indent=2))
