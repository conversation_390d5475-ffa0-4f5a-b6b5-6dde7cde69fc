from log import get_logger

from src.interfaces.vault_interface import IVaultData
from config.config import PRIMARY_CHAIN_ID
from src.market_data import MarketData
from utils.web3_contract import Web3Contract
from utils.web3_connector import Web3Connector
from config import config

logger = get_logger(__name__)

# Cache expiration time in seconds (5 minutes)
CACHE_EXPIRATION_SECONDS = 300


class VaultData(IVaultData):
    """
    Vault data implementation using The Graph GraphQL endpoint.

    This class implements the IVaultData interface and provides vault data
    collection from Morpho Blue subgraphs via The Graph.
    """

    def __init__(self, vault_address: str, chain_id: int = PRIMARY_CHAIN_ID):
        """
        Initialize VaultData instance.

        Args:
            vault_address: The vault address to fetch data for
            chain_id: Chain ID to fetch data from (default: Base)
        """
        self.vault_address = vault_address
        self.chain_id = chain_id
        self.client = Web3Connector(provider_url=config.PRIMARY_RPC_URL).get_client()
        self.vault_contract = Web3Contract(self.client, vault_address, config.VAULT_ABI)
        self.morpho_contract = Web3Contract(self.client, config.MORPHO_ADDRESS, config.MORPHO_ABI)

        self.data = self.fetch_vault_data()


    def fetch_vault_data(self) -> dict:
        """
        Fetch complete vault data using The Graph GraphQL endpoint.

        This method implements the IVaultData.fetch_vault_data interface.
        Returns data in the exact format specified by the interface.

        Args:
            vault_address: The vault address to fetch data for

        Returns:
            Vault data in interface format:
            {
                "vault_address": "",
                "market_data": [
                    MarketData(
                        data={
                            "chain_id": "",
                            "id": "",
                            "params": {
                                "loan_token": "",
                                "collateral_token": "",
                                "irm": "",
                                "oracle": "",
                                "lltv": "",
                            },
                            "state": {
                                "total_supply_assets": "",
                                "total_supply_shares": "",
                                "total_borrow_assets": "",
                                "total_borrow_shares": "",
                                "last_update": "",
                                "fee": "",
                            },
                            "cap": "",
                            "vault_assets": "",
                            "rate_at_target": "",
                        },
                    )
                ],
            }
        """
        try:
            market_data_list = []

            for market_id in self.get_vault_markets():
                market_data = MarketData(market_id, chain_id=self.chain_id)
                market_data_list.append(market_data)

            vault_data = {
                "vault_address": self.vault_address,
                "market_data": market_data_list,
            }

            return vault_data

        except Exception as e:
            logger.error(f"Failed to fetch market data for vault {self.vault_address}: {e}")
            raise

    def get_vault_markets(self):
        """
        Get list of market IDs associated with the vault.

        Args:
            vault_address: Vault address (uses instance address if None)

        Returns:
            List of market IDs
        """
        withdraw_queue_length = self.vault_contract.call("withdrawQueueLength")

        for i in range(withdraw_queue_length): 
            market_id = self.vault_contract.call("withdrawQueue", [i])
            yield market_id