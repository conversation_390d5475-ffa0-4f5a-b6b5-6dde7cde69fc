# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# VS Code
.vscode/

# macOS
.DS_Store

# System files
Thumbs.db
ehthumbs.db
Icon?
Desktop.ini

# Logs
*.log

# dotenv
*.env

# Local settings
local_settings.py

# Morphological reallocator bot specific
logs/
data/

# Terraform
.terraform/
terraform.tfstate
terraform.tfstate.backup
.terraform.lock.hcl
