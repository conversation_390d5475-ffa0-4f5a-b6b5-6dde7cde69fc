from typing import List, Optional, Any
import logging
from src.market_data import MarketData
from src.vault_data import VaultData
from utils.math import (
    get_depositable_amount,
    get_withdrawable_amount,
    get_utilization,
)
from utils.reallocation_validator import (
    validate_reallocation_consistency,
)
from strategies.strategy import Strategy
from config import config
from log import get_logger

logger = get_logger(__name__)
logger.setLevel(logging.DEBUG)


class PrioritizedUtilizationStrategy(Strategy):
    def find_reallocation(self, vault_data: VaultData) -> Optional[List[Any]]:
        market_lookup = {m.data["id"]: m for m in vault_data["market_data"]}

        total_withdrawable = 0
        total_depositable = 0
        total_vault_balance = 0
        withdrawals = []
        deposits = []

        market_ids_reallocated = []

        # Track actual amounts for consistency validation
        actual_withdrawn = 0
        actual_deposited = 0

        # Preprocessing: evaluate supply/demand
        for market_id in config.priority_list:
            market_data: MarketData = market_lookup.get(market_id)
            if market_data is None or config.market_names.get(market_id) == "Idle":
                continue

            total_vault_balance += market_data.data["vault_assets"]

            utilization = get_utilization(market_data.state)
            target = config.target_util.get(market_id)

            # Skip markets with no target or zero utilization
            # Note: Allow processing Idle markets for potential withdrawals
            if target is None or utilization == 0:
                continue

            if utilization > target:
                total_depositable += get_depositable_amount(market_data, target)
            elif utilization < target:
                total_withdrawable += get_withdrawable_amount(market_data, target)

        to_reallocate = min(total_withdrawable, total_depositable)
        if to_reallocate == 0:
            return None

        remaining_withdraw = to_reallocate
        remaining_deposit = to_reallocate

        logger.debug(f"Total amount to reallocate: {float(to_reallocate)/1e6:.2f} USDC")
        logger.debug(f"Total vault balance: {float(total_vault_balance)/1e6:.2f} USDC")

        # Phase 1: Withdraw from markets below target (to increase utilization)
        for market_id in config.priority_list:
            if remaining_withdraw == 0:
                break

            market_data = market_lookup.get(market_id)
            if market_data is None:
                continue

            utilization = get_utilization(market_data.state)
            target = config.target_util.get(market_id)

            # Skip markets with no target or at/above target
            # Withdraw from under-utilized markets (to increase their utilization)
            if target is None or utilization >= target:
                continue

            withdrawable = get_withdrawable_amount(market_data, target)
            amount = min(withdrawable, remaining_withdraw)
            remaining_withdraw -= amount
            actual_withdrawn += amount  # Track actual withdrawn amount

            vault_assets_before = market_data.data["vault_assets"]
            supply_assets = market_data.data["state"]["total_supply_assets"]
            borrow_assets = market_data.data["state"]["total_borrow_assets"]

            utilization_before = (
                (borrow_assets / supply_assets * 100) if supply_assets > 0 else 0
            )
            target = config.target_util.get(market_id, 0)
            target_pct = float(target) / 1e18 * 100 if target > 0 else 0
            utilization_after = (
                (borrow_assets / (supply_assets - amount) * 100)
                if supply_assets > 0
                else 0
            )

            priority = "N/A"
            if market_id in config.priority_list:
                priority = f"#{config.priority_list.index(market_id) + 1}"

            logger.debug(f"{config.market_names[market_id]}: Priority {priority}:")
            logger.debug(f"  Vault Assets Before: {vault_assets_before/1e6:.2f} USDC")
            logger.debug(
                f"  Utilization Before: {utilization_before:.1f}% (Target: {target_pct:.1f}%)"
            )
            logger.debug(f"  Gap: {utilization_before - target_pct:+.1f}%")
            if amount != 0:
                logger.debug(f"  Withdrawal: {float(amount)/1e6:+.1f}")
                logger.debug(
                    f"  Utilization After: {utilization_after:.1f}% (Target: {target_pct:.1f}%)"
                )
            else:
                logger.debug("  No withdrawal")

            withdrawals.append(
                (market_data.params, market_data.data["vault_assets"] - amount)
            )

            market_ids_reallocated.append(market_id)

        # Phase 2: Deposit into markets above target (to decrease utilization)
        for market_id in config.priority_list:
            if remaining_deposit == 0:
                break

            market_data = market_lookup.get(market_id)
            if market_data is None:
                continue

            # Skip Idle market in this phase - handle it separately at the end
            if config.market_names.get(market_id) == "Idle":
                continue

            utilization = get_utilization(market_data.state)
            target = config.target_util.get(market_id)

            # Skip markets with no target, at/below target, or Idle markets (100% target = do not allocate)
            # Deposit to over-utilized markets (to decrease their utilization)
            if target is None or utilization <= target or target >= config.WAD:
                continue

            depositable = get_depositable_amount(market_data, target)
            amount = min(depositable, remaining_deposit)
            remaining_deposit -= amount
            actual_deposited += amount  # Track actual deposited amount

            vault_assets_before = market_data.data["vault_assets"]
            deposit_cap = market_data.data["cap"]
            supply_assets = market_data.data["state"]["total_supply_assets"]
            borrow_assets = market_data.data["state"]["total_borrow_assets"]

            utilization_before = (
                (borrow_assets / supply_assets * 100) if supply_assets > 0 else 0
            )
            target = config.target_util.get(market_id, 0)
            target_pct = float(target) / 1e18 * 100 if target > 0 else 0
            utilization_after = (
                (borrow_assets / (supply_assets + amount) * 100)
                if supply_assets > 0
                else 0
            )

            priority = "N/A"
            if market_id in config.priority_list:
                priority = f"#{config.priority_list.index(market_id) + 1}"

            logger.debug(f"{config.market_names[market_id]}: Priority {priority}:")
            logger.debug(f"  Vault Assets Before: {vault_assets_before/1e6:.2f} USDC")
            logger.debug(f"  Deposit Cap: {float(deposit_cap)/1e6:.2f} USDC")
            logger.debug(
                f"  Utilization Before: {utilization_before:.1f}% (Target: {target_pct:.1f}%)"
            )
            logger.debug(f"  Gap: {utilization_before - target_pct:+.1f}%")
            if amount != 0:
                logger.debug(f"  Deposit: {float(amount)/1e6:+.1f}")
                logger.debug(
                    f"  Utilization After: {utilization_after:.1f}% (Target: {target_pct:.1f}%)"
                )
            else:
                logger.debug("  No deposit")

            deposits.append(
                (market_data.params, market_data.data["vault_assets"] + amount)
            )

            market_ids_reallocated.append(market_id)

        # Phase 3: Handle remainder amounts and ensure consistency
        remainder = actual_withdrawn - actual_deposited

        logger.debug(f"Actual withdrawn: {float(actual_withdrawn)/1e6:.6f} USDC")
        logger.debug(f"Actual deposited: {float(actual_deposited)/1e6:.6f} USDC")
        logger.debug(f"Remainder: {float(remainder)/1e6:.6f} USDC")

        # Allocate max_uint256 to Idle market
        idle_market_id = (
            "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb"
        )
        idle_market_data = market_lookup.get(idle_market_id)

        deposits.append(
            (
                idle_market_data.params,
                config.MAX_UINT_256,  # Idle market gets max allocation for excess funds
            )
        )

        # Final consistency check
        if actual_withdrawn != actual_deposited:
            logger.error(
                f"CONSISTENCY ERROR: withdrawn={float(actual_withdrawn)/1e6:.6f} != deposited={float(actual_deposited)/1e6:.6f}"
            )
            return None

        if not withdrawals and not deposits:
            return None

        result = withdrawals + deposits

        # Additional validation using the validator utility
        # Filter current allocations to include only markets in the result
        current_allocations = {
            m.data["id"]: m.data["vault_assets"]
            for m in vault_data["market_data"]
            if m.data["id"] in market_ids_reallocated
        }

        is_valid, error_msg = validate_reallocation_consistency(
            result, current_allocations
        )

        if not is_valid:
            logger.error(f"Reallocation validation failed: {error_msg}")
            return None

        logger.debug(
            f"Final consistency check passed: {float(actual_withdrawn)/1e6:.6f} USDC withdrawn = {float(actual_deposited)/1e6:.6f} USDC deposited"
        )
        return result
