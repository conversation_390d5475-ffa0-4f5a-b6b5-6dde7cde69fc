export DOCKER_DEFAULT_PLATFORM=linux/amd64
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 236840575805.dkr.ecr.eu-central-1.amazonaws.com

# Create and use a new builder instance
docker buildx create --use --name mybuilder
docker buildx inspect mybuilder --bootstrap

# Build the Docker image with the correct platform and format
docker buildx build --platform linux/amd64 --output "type=docker" -t morpho-reallocator-bot-handler .

# Tag and push the Docker image
docker tag morpho-reallocator-bot-handler:latest 236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-handler
docker push 236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-handler

# Update the AWS Lambda function with the new image URI
aws lambda update-function-code --function-name morpho-reallocator-bot-handler --image-uri 236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-handler:latest

# Remove the builder instance
docker buildx rm mybuilder
