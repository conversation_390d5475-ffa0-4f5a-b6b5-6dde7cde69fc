from typing import Optional, <PERSON>ple
import requests
import os
import sys

sys.path.append(os.getcwd())

from web3 import Web3
import base64
import json
import datetime
import ecdsa
import hashlib
from enum import Enum
from log import get_logger

from utils.load_cloud_env import load_cloud_env

load_cloud_env()

logger = get_logger(__name__)


class TransactionType(Enum):
    EVM_TRANSACTION = "evm_transaction"
    EVM_MESSAGE = "evm_message"


# chains
class ForDefiChain(Enum):
    ARBITRUM_MAINNET = "arbitrum_mainnet"
    AVALANCHE_MAINNET = "avalanche_mainnet"
    BINANCE_MAINNET = "bsc_mainnet"
    ETHEREUM_MAINNET = "ethereum_mainnet"
    OPTIMISM_MAINNET = "optimism_mainnet"
    POLYGON_MAINNET = "polygon_mainnet"
    MANTLE_MAINNET = "mantle_mainnet"
    MODE_MAINNET = 34443
    BASE_MAINNET = 8453


chain_id_to_fordefi_chain = {
    "42161": ForDefiChain.ARBITRUM_MAINNET,
    "43114": ForDefiChain.AVALANCHE_MAINNET,
    "56": ForDefiChain.BINANCE_MAINNET,
    "1": ForDefiChain.ETHEREUM_MAINNET,
    "10": ForDefiChain.OPTIMISM_MAINNET,
    "137": ForDefiChain.POLYGON_MAINNET,
    "5000": ForDefiChain.MANTLE_MAINNET,
    "34443": ForDefiChain.MODE_MAINNET,
    "8453": ForDefiChain.BASE_MAINNET,
}


class TransactionState(Enum):
    COMPLETED = "completed"
    STUCK = "stuck"
    REVERTED = "reverted"
    ABORTED = "aborted"


def wait_until_final_state(
    client: Web3,
    transaction_id,
) -> Tuple[Optional[str], Optional[TransactionState]]:

    try:
        bearer_token = os.getenv("FORDEFI_BEARER_TOKEN")
        url = f"https://api.fordefi.com/api/v1/transactions/{transaction_id}"

        if not bearer_token:
            raise ValueError("Missing FORDEFI_BEARER_TOKEN")

        headers = {
            "Authorization": f"Bearer {bearer_token}",
        }

        response = requests.get(url, headers=headers)

        if response.status_code in [200, 201, 204]:
            transaction_data = response.json()
            tx_hash: str = transaction_data.get("hash", None)
            if not tx_hash:
                logger.error(f"Transaction hash not found: {transaction_data}")
                return None, None
            receipt = client.eth.wait_for_transaction_receipt(tx_hash)
            if receipt["status"] == 0:
                return tx_hash, TransactionState.REVERTED.value
            else:
                return tx_hash, TransactionState.COMPLETED.value
        else:
            return None, None
    except Exception as e:
        logger.error(f"Getting transaction hash: {str(e)}", exc_info=True)
        return None, None


def create_transaction(
    client,
    chain_id,
    raw_data,
    vault_id,
    to,
    value,
    transaction_type=TransactionType.EVM_TRANSACTION.value,
) -> Tuple[Optional[str], Optional[TransactionState]]:
    bearer_token = os.getenv("FORDEFI_BEARER_TOKEN")
    chain = chain_id_to_fordefi_chain[str(chain_id)].value

    timestamp = datetime.datetime.now().strftime("%s")

    if not bearer_token:
        raise ValueError("Missing FORDEFI_BEARER_TOKEN")

    path = "/api/v1/transactions/create-and-wait"
    url = f"https://api.fordefi.com{path}"

    request_body = {
        "signer_type": "api_signer",
        "type": transaction_type,
        "details": {
            "type": "evm_raw_transaction",
            "chain": chain,
            "to": to,
            "value": str(value),
            "data": {"type": "hex", "hex_data": raw_data},
            "gas": {
                "type": "priority",
                "priority_level": "medium",
            },
        },
        "vault_id": vault_id,
        "wait_for_state": "completed",
    }

    payload = f"{path}|{timestamp}|{json.dumps(request_body)}"
    key_str = os.getenv("FORDEFI_PRIVATE_KEY")
    if not key_str:
        # read the private key from the file
        with open("private.pem", "r") as file:
            key_str = file.read()
    else:
        # format the key string
        key_str = key_str.replace("\\n", "\n")

    signing_key = ecdsa.SigningKey.from_pem(key_str)

    logger.info(f"SigningKey: {signing_key}")

    signature = signing_key.sign(
        data=payload.encode(),
        hashfunc=hashlib.sha256,
        sigencode=ecdsa.util.sigencode_der,
    )
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "x-signature": base64.b64encode(signature),
        "x-timestamp": timestamp.encode(),
    }

    response = requests.post(url, json=request_body, headers=headers)

    if response.status_code in [200, 201]:
        tx_id = response.json()["id"]
        return wait_until_final_state(client, tx_id)
    else:
        logger.error(f"Creating transaction: {response.json()}")
        return None, None
