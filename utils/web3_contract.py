import json
import time
from web3 import Web3
from web3.exceptions import ContractLogicError, BlockNumberOutofRange, ValidationError

from log import get_logger

logger = get_logger(__name__)


class Web3Contract:
    def __init__(self, client: Web3, contract_address: str, abi: json):
        self.client = client
        self.contract_address = Web3.to_checksum_address(contract_address)
        self.abi = abi
        self.contract = self.client.eth.contract(
            address=self.contract_address, abi=self.abi
        )

    def get_contract(self, pool_address=None, abi=None):
        if pool_address is None and abi is None:
            return self.contract
        else:
            return self.client.eth.contract(address=pool_address, abi=abi)

    def has_function(self, function_name):
        if self.contract.find_functions_by_name(function_name) == []:
            return False
        else:
            return True

    def call(
        self,
        function_name,
        params=[],
        contract_address=None,
        from_address=None,
        from_block="latest",
    ):
        if self.has_function(function_name) is False:
            logger.exception(
                f'Function "{function_name}" does not exist in {self.contract_address}',
                exc_info=True,
            )
            raise Exception(
                f"{function_name} doesn't exist in the contract : {self.contract_address}"
            )
        else:
            for i in range(5):
                try:
                    contract = self.get_contract(contract_address)
                    function = eval("contract.functions." + function_name)
                    from_param = {"from": from_address} if from_address else None
                    from_block = from_block if from_block else "latest"
                    result = function(*params).call(
                        from_param, block_identifier=from_block
                    )
                    return result
                except ValidationError as e:
                    logger.warning(
                        f"ValidationError on function {function_name}: {e}",
                    )
                    return
                except ContractLogicError as e:
                    logger.warning(
                        f"ContractLogicError on function {function_name}: {e}",
                    )
                    return
                except BlockNumberOutofRange as e:
                    logger.warning(f"Block number {from_block} is out of range: {e}")
                    return
                except Exception as e:
                    logger.warning(
                        f"ConnectionError on function {function_name}: {e}. Retrying in {2**i} seconds",
                        exc_info=True,
                    )
                    time.sleep(2**i)
