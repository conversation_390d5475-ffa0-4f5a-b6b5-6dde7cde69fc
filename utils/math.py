from decimal import Decimal
from typing import Union
from config import config

YEAR = 60 * 60 * 24 * 365

VIRTUAL_ASSETS = 1
VIRTUAL_SHARES = 10**6

# Convert config.WAD to integer for mathematical operations
WAD_INT = int(config.WAD)


def to_int(value: Union[int, Decimal]) -> int:
    """
    Convert a value to integer, handling both int and Decimal types.

    Args:
        value: The value to convert (int or Decimal)

    Returns:
        Integer representation of the value
    """
    if isinstance(value, Decimal):
        return int(value)
    return value


def min_value(a: int, b: int) -> int:
    return a if a < b else b

def max_value(a: int, b: int) -> int:
    return a if a > b else b

def mul_div_down(x: int, y: int, d: int) -> int:
    """Multiply x by y and divide by d, rounding down."""
    if d == 0:
        return 0
    return (x * y) // d


def mul_div_up(x: int, y: int, d: int) -> int:
    """Multiply x by y and divide by d, rounding up."""
    if d == 0:
        return 0
    return (x * y + (d - 1)) // d


def safe_mul_div(x: Union[int, Decimal], y: Union[int, Decimal], d: Union[int, Decimal], round_up: bool = False) -> int:
    """
    Safe multiplication and division with overflow protection.
    Returns 0 if any parameter is 0 or if overflow would occur.
    Handles both int and Decimal types by converting to int.
    """
    # Convert all parameters to integers
    x_int = to_int(x)
    y_int = to_int(y)
    d_int = to_int(d)

    if x_int == 0 or y_int == 0 or d_int == 0:
        return 0

    # Check for potential overflow in multiplication
    # Use try-except to handle any edge cases with large numbers
    try:
        if x_int > 0 and y_int > 0 and x_int > (2**256 - 1) // y_int:
            return 0
    except (OverflowError, ZeroDivisionError):
        return 0

    if round_up:
        return mul_div_up(x_int, y_int, d_int)
    else:
        return mul_div_down(x_int, y_int, d_int)


def w_div_down(x: Union[int, Decimal], y: Union[int, Decimal]) -> int:
    """Divide x by y, multiplied by WAD, rounding down. Formula: (x * WAD) / y"""
    return mul_div_down(to_int(x), WAD_INT, to_int(y))


def w_div_up(x: Union[int, Decimal], y: Union[int, Decimal]) -> int:
    """Divide x by y, multiplied by WAD, rounding up. Formula: (x * WAD) / y"""
    return mul_div_up(to_int(x), WAD_INT, to_int(y))


def w_mul_down(x: Union[int, Decimal], y: Union[int, Decimal]) -> int:
    """Multiply x by y, divided by WAD, rounding down. Formula: (x * y) / WAD"""
    return mul_div_down(to_int(x), to_int(y), WAD_INT)


def to_assets_up(shares: int, total_assets: int, total_shares: int) -> int:
    return mul_div_up(
        shares, total_assets + VIRTUAL_ASSETS, total_shares + VIRTUAL_SHARES
    )


def to_assets_down(shares: int, total_assets: int, total_shares: int) -> int:
    return mul_div_down(
        shares, total_assets + VIRTUAL_ASSETS, total_shares + VIRTUAL_SHARES
    )


def to_shares_up(assets: int, total_assets: int, total_shares: int) -> int:
    return mul_div_up(
        assets, total_shares + VIRTUAL_SHARES, total_assets + VIRTUAL_ASSETS
    )


def to_shares_down(assets: int, total_assets: int, total_shares: int) -> int:
    return mul_div_down(
        assets, total_shares + VIRTUAL_SHARES, total_assets + VIRTUAL_ASSETS
    )


def bound(x: int, min_val: int, max_val: int) -> int:
    if x < min_val:
        return min_val
    if x > max_val:
        return max_val
    return x


def w_taylor_compounded(x: int, n: int) -> int:
    """Calculate Taylor series approximation for compound interest."""
    first_term = x * n
    second_term = mul_div_down(first_term, first_term, 2 * WAD_INT)
    third_term = mul_div_down(second_term, first_term, 3 * WAD_INT)
    return first_term + second_term + third_term


def get_utilization(market_state: dict) -> int:
    """Calculate market utilization ratio with safety checks."""
    total_supply = market_state["total_supply_assets"]
    total_borrow = market_state["total_borrow_assets"]

    if total_supply == 0:
        return 0

    return w_div_down(total_borrow, total_supply)


def get_withdrawal_to_utilization(market_state: dict, target_utilization: Union[int, Decimal]) -> int:
    """
    Calculate the amount to withdraw to reach target utilization.
    Returns 0 if no withdrawal is needed or if calculation would be invalid.
    """
    current_utilization = get_utilization(market_state)
    total_supply = market_state["total_supply_assets"]
    target_util_int = to_int(target_utilization)

    if current_utilization >= target_util_int or total_supply == 0:
        return 0

    if target_util_int == 0:
        return 0

    ratio = w_div_down(current_utilization, target_util_int)
    if ratio >= WAD_INT:
        return 0

    # Calculate the multiplier: 1 - ratio
    # This represents the fraction of supply that needs to be withdrawn
    # SAFETY CHECK: Ensure ratio <= WAD to prevent underflow
    if ratio > WAD_INT:
        return 0

    multiplier = WAD_INT - ratio

    return safe_mul_div(total_supply, multiplier, WAD_INT)


def get_deposit_to_utilization(market_state: dict, target_utilization: Union[int, Decimal]) -> int:
    """
    Calculate the amount to deposit to reach target utilization.
    Returns 0 if no deposit is needed or if calculation would be invalid.
    """
    current_utilization = get_utilization(market_state)
    total_supply = market_state["total_supply_assets"]
    target_util_int = to_int(target_utilization)

    if current_utilization <= target_util_int or total_supply == 0:
        return 0

    if current_utilization == 0 or target_util_int == 0:
        return 0

    ratio = w_div_down(target_util_int, current_utilization)
    if ratio >= WAD_INT:
        return 0

    # Calculate the multiplier: (1/ratio) - 1
    # This represents how much supply needs to increase to reach target utilization
    if ratio == 0:
        return 0

    inverse_ratio = w_div_down(WAD_INT, ratio)

    # CRITICAL FIX: Prevent integer underflow when inverse_ratio < WAD
    # This happens when current_utilization is much lower than target_utilization
    if inverse_ratio <= WAD_INT:
        return 0

    multiplier = inverse_ratio - WAD_INT

    return safe_mul_div(total_supply, multiplier, WAD_INT)


def get_withdrawable_amount(market_data: dict, target_utilization: Union[int, Decimal]) -> int:
    """Calculate the maximum withdrawable amount for a market to reach target utilization."""
    return min_value(
        get_withdrawal_to_utilization(market_data.data["state"], target_utilization),
        market_data.data["vault_assets"],
    )


def get_depositable_amount(market_data: dict, target_utilization: Union[int, Decimal]) -> int:
    """Calculate the maximum depositable amount for a market to reach target utilization."""
    return min_value(
        get_deposit_to_utilization(market_data.data["state"], target_utilization),
        max_value(0, market_data.data["cap"] - market_data.data["vault_assets"]),
    )


def apy_to_rate(apy: int) -> int:
    first_term = apy
    second_term = w_mul_down(first_term, first_term)
    third_term = w_mul_down(second_term, first_term)
    apr = first_term - second_term // 2 + third_term // 3
    return apr // YEAR


def rate_to_utilization(rate: int, rate_at_target: int) -> int:
    """Convert interest rate to utilization ratio."""
    max_rate = config.CURVE_STEEPNESS * rate_at_target
    min_rate = rate_at_target // config.CURVE_STEEPNESS
    utilization = 0

    target_util_int = to_int(config.TARGET_UTILIZATION)

    if rate >= max_rate:
        utilization = WAD_INT
    elif rate >= rate_at_target:
        utilization = target_util_int + mul_div_down(
            WAD_INT - target_util_int,
            rate - rate_at_target,
            max_rate - rate_at_target,
        )
    elif rate > min_rate:
        utilization = mul_div_down(
            target_util_int, rate - min_rate, rate_at_target - min_rate
        )
    return utilization


def utilization_to_rate(utilization: int, rate_at_target: int) -> int:
    """Convert utilization ratio to interest rate."""
    max_rate = config.CURVE_STEEPNESS * rate_at_target
    min_rate = rate_at_target // config.CURVE_STEEPNESS
    rate = min_rate

    target_util_int = to_int(config.TARGET_UTILIZATION)

    if utilization >= WAD_INT:
        rate = max_rate
    elif utilization >= rate_at_target:
        rate = rate_at_target + mul_div_down(
            max_rate - rate_at_target,
            utilization - target_util_int,
            WAD_INT - target_util_int,
        )
    elif utilization > min_rate:
        rate = min_rate + mul_div_down(
            rate_at_target - min_rate, utilization, target_util_int
        )
    return rate


def percent_to_wad(percent: float) -> int:
    return int(Decimal(percent) * Decimal("1e16"))


def rate_to_apy(rate: int) -> int:
    return w_taylor_compounded(rate, YEAR)
