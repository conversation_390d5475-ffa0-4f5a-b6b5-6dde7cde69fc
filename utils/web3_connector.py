from web3.middleware import geth_poa_middleware
from web3 import Web3

from log import get_logger

logger = get_logger(__name__)


class Web3Connector:
    def __init__(self, provider_url: str):
        self.provider_url = provider_url
        self.connect_to_client()

    def connect_to_client(self):
        try:
            self.client = Web3(
                Web3.HTTPProvider(self.provider_url, request_kwargs={"timeout": 60})
            )
            self.client.middleware_onion.inject(geth_poa_middleware, layer=0)
        except Exception:
            logger.exception("Couldn't connect to: " + self.provider_url)
            raise Exception("Couldn't connect to: " + self.provider_url)

    def is_connected(self):
        return self.client.isConnected()

    def get_client(self):
        return self.client
