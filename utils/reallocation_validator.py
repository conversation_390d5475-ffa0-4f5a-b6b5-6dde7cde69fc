"""
Reallocation validation utilities to prevent InconsistentReallocation errors.

This module provides functions to validate reallocation proposals before
they are submitted to the contract, ensuring that total withdrawals exactly
equal total supplies.
"""

from typing import List, <PERSON><PERSON>, Any, Dict
from log import get_logger

logger = get_logger(__name__)


def validate_reallocation_consistency(
    reallocation: List[Tuple[Any, int]], current_allocations: Dict[str, int]
) -> <PERSON><PERSON>[bool, str]:
    """
    Validate that a reallocation proposal maintains consistency.

    Args:
        reallocation: List of (market_params, new_allocation) tuples
        current_allocations: Dict mapping market_id to current vault_assets

    Returns:
        Tuple of (is_valid, error_message, allocation_changes)
    """
    if not reallocation:
        return True, "No reallocation to validate", {}

    total_current = sum(current_allocations.values())
    total_new = 0

    # Calculate new total and track changes
    for _, new_allocation in reallocation:

        # Handle max_uint256 allocation (Idle market)
        if new_allocation == 2**256 - 1:  # MAX_UINT_256
            # For Idle market, calculate the remainder
            temp_total = sum(alloc for _, alloc in reallocation if alloc != 2**256 - 1)
            new_allocation = total_current - temp_total

        total_new += new_allocation

    # Validate consistency
    if total_current != total_new:
        error_msg = (
            f"Inconsistent reallocation: current total {total_current} "
            f"!= new total {total_new} (difference: {total_new - total_current})"
        )
        return False, error_msg

    return True, "Reallocation is consistent"
