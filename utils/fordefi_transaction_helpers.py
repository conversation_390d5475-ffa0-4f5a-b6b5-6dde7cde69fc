# transaction_helpers.py
import os
import sys

sys.path.append(os.getcwd())

import time
from web3 import Web3
from typing import Optional
from utils.create_transaction import TransactionState, create_transaction
from log import get_logger
from config import config

MAX_UINT256 = 2**256 - 1
PROVIDER_URL = config.PRIMARY_RPC_URL

logger = get_logger(__name__)


def fordefi_sign_and_send_transaction(
    client, chain_id, vault_id, transaction
) -> Optional[str]:
    tx_hash, state = create_transaction(
        client,
        chain_id,
        transaction["data"],
        vault_id,
        transaction["to"],
        transaction["value"],
    )

    if tx_hash is None or state != TransactionState.COMPLETED.value:
        logger.error(f"Signing transaction: {tx_hash} - {state}")
        raise Exception("Signing transaction")

    return tx_hash


def wait_for_transaction_completion(web3_client: Web3, transaction_hash):
    transaction_status = web3_client.eth.get_transaction_receipt(transaction_hash)[
        "status"
    ]
    retries = 0
    while transaction_status == 0 and retries < 5:
        time.sleep(1)
        transaction_status = web3_client.eth.get_transaction_receipt(transaction_hash)[
            "status"
        ]
        retries += 1


def build_transaction(web3_client: Web3, func, params, account_address):
    """
    Parameters should be in a list if the contract is expecting one or more native data types.
    Parameters should be in a tuple if the contract is expecting a struct.
    """
    retries = 0
    while retries < 5:
        try:
            if isinstance(params, tuple):
                transaction = func(params).build_transaction(
                    {
                        "from": account_address,
                        "nonce": web3_client.eth.get_transaction_count(account_address),
                    }
                )
            elif isinstance(params, list):
                transaction = func(*params).build_transaction(
                    {
                        "from": account_address,
                        "nonce": web3_client.eth.get_transaction_count(account_address),
                    }
                )
            else:
                raise ValueError("Invalid params type")
            return transaction
        except Exception as e:
            logger.warning(f"Retrying transaction build: {e}")
            retries += 1
            time.sleep(1)

    logger.error(f"Failed to build transaction after {retries} retries.")
    return None


def build_and_send_transaction(
    chain_id, vault_id, web3_client, func, params, account_address
):
    transaction = build_transaction(web3_client, func, params, account_address)
    if transaction is None:
        return None
    return fordefi_sign_and_send_transaction(
        web3_client, chain_id, vault_id, transaction
    )
